import React from 'react';
import { BatchConverter } from '../components/BatchConverter';

const BatchConvertPage: React.FC = () => {
  const handleError = (error: string) => {
    // You can integrate with your toast notification system here
    console.error('Batch conversion error:', error);
    alert(error); // Simple alert for now
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Batch Converter
            </h1>
            <p className="text-gray-600">
              Convert multiple Mermaid (.mmd) files to images in batch with progress tracking and parallel processing.
            </p>
          </div>

          <div className="mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Features:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Upload multiple .mmd files at once</li>
                <li>• Real-time conversion progress tracking</li>
                <li>• Parallel processing for faster conversion</li>
                <li>• Configurable output settings (format, quality, theme)</li>
                <li>• Bulk download of converted files</li>
                <li>• Error handling and retry capabilities</li>
              </ul>
            </div>
          </div>

          <BatchConverter onError={handleError} />

          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Tips for Best Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">File Preparation</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Ensure all files have .mmd extension</li>
                  <li>• Validate Mermaid syntax before upload</li>
                  <li>• Keep file sizes reasonable (&lt; 1MB each)</li>
                  <li>• Use descriptive filenames</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Performance Tips</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Process in smaller batches for better performance</li>
                  <li>• Use PNG for diagrams with transparency</li>
                  <li>• Use JPEG for smaller file sizes</li>
                  <li>• SVG format preserves vector quality</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">Processing Limits</h4>
            <p className="text-sm text-yellow-700">
              For optimal performance, we process files in batches of 3 simultaneously. 
              Large batches may take longer to complete. You can monitor progress for each file individually.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BatchConvertPage;
