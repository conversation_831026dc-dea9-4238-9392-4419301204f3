graph TB
    %% Customer Interface Layer
    Customer[👤 Customer] --> Interface{Interface Layer}
    Interface --> WebChat[💬 Web Chat Widget]
    Interface --> MobileApp[📱 Mobile App]
    Interface --> API[🔌 REST API]

    %% API Gateway & Load Balancer
    WebChat --> LB[⚖️ Load Balancer]
    MobileApp --> LB
    API --> LB
    LB --> Gateway[🚪 API Gateway<br/>Rate Limiting & Auth]

    %% Core Agent System
    Gateway --> RouterAgent[🧠 Router Agent<br/>Intent Classification]
    
    %% Multi-Agent Architecture
    RouterAgent --> ProductAgent[🛍️ Product Expert Agent]
    RouterAgent --> OrderAgent[📦 Order Specialist Agent]
    RouterAgent --> PolicyAgent[📋 Policy Advisor Agent]
    RouterAgent --> TechAgent[🔧 Technical Support Agent]
    RouterAgent --> QAAgent[✅ Quality Assurance Agent]

    %% Data Layer
    ProductAgent --> DataAccess{Data Access Layer}
    OrderAgent --> DataAccess
    PolicyAgent --> DataAccess
    TechAgent --> DataAccess
    
    DataAccess --> PrimaryDB[(🗄️ PostgreSQL<br/>Products, Orders, Customers)]
    DataAccess --> VectorDB[(🧠 Vector Database<br/>Knowledge Base, FAQs)]
    DataAccess --> Cache[(⚡ Redis Cache<br/>Session & Frequent Data)]

    %% External Integrations
    DataAccess --> ShopifyAPI[🛒 Shopify API<br/>GraphQL/REST]
    DataAccess --> WebhookService[📡 Webhook Handler<br/>Real-time Updates]
    
    %% Knowledge Management
    VectorDB --> KnowledgeSync[🔄 Knowledge Sync Service]
    KnowledgeSync --> CMS[📝 Content Management]
    KnowledgeSync --> BlogCrawler[🕷️ Blog Crawler]
    
    %% Monitoring & Analytics
    QAAgent --> Monitor[📊 Monitoring Service]
    Monitor --> Alerts[🚨 Alert System]
    Monitor --> Analytics[📈 Analytics Dashboard]

    %% Styling
    classDef agent fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef external fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef interface fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class RouterAgent,ProductAgent,OrderAgent,PolicyAgent,TechAgent,QAAgent agent
    class PrimaryDB,VectorDB,Cache data
    class ShopifyAPI,WebhookService,CMS,BlogCrawler external
    class Customer,Interface,WebChat,MobileApp,API interface