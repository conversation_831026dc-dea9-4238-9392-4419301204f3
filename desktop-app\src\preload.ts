import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron';

// Define the API interface
interface ElectronAPI {
  selectFiles: () => Promise<{ canceled: boolean; files?: any[] }>;
  selectFolder: () => Promise<{ canceled: boolean; path?: string }>;
  selectOutputFolder: () => Promise<{ canceled: boolean; path?: string }>;
  convertFile: (fileData: any, options: any) => Promise<any>;
  convertBatch: (files: any[], options: any) => Promise<any>;
  openOutputFolder: (folderPath: string) => Promise<void>;
  generateAIDiagram: (options: {
    description: string;
    outputFormat: 'mermaid' | 'png' | 'jpg' | 'svg';
    template?: string;
    theme: string;
  }) => Promise<{
    success: boolean;
    content?: string;
    imageData?: string;
    outputFormat?: string;
    error?: string;
  }>;
  saveAIResult: (result: {
    content: string;
    imageData?: string;
    outputFormat: string;
  }) => Promise<{
    success: boolean;
    filePath?: string;
    error?: string;
  }>;
  getAppInfo: () => Promise<{ name: string; version: string; platform: string }>;
  onConversionProgress: (callback: (data: any) => void) => void;
  removeAllListeners: (channel: string) => void;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI: ElectronAPI = {
  selectFiles: () => ipcRenderer.invoke('select-files'),
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  selectOutputFolder: () => ipcRenderer.invoke('select-output-folder'),
  convertFile: (fileData: any, options: any) => ipcRenderer.invoke('convert-file', fileData, options),
  convertBatch: (files: any[], options: any) => ipcRenderer.invoke('convert-batch', files, options),
  openOutputFolder: (folderPath: string) => ipcRenderer.invoke('open-output-folder', folderPath),
  generateAIDiagram: (options) => ipcRenderer.invoke('generate-ai-diagram', options),
  saveAIResult: (result) => ipcRenderer.invoke('save-ai-result', result),
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),
  onConversionProgress: (callback: (data: any) => void) => {
    ipcRenderer.on('conversion-progress', (event, data) => callback(data));
  },
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
};

contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type declaration for TypeScript
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
