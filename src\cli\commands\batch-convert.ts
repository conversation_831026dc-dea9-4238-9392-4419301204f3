/**
 * Batch Convert Command for ArchitekAI CLI
 * 
 * Advanced batch processing with progress tracking, parallel processing, and smart organization.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import fs from 'fs-extra';
import { glob } from 'glob';
import { ConfigManager } from '@/core/config/manager';
import { MermaidConverter, ConversionOptions } from '@/core/converter';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner,
  displayError,
  displayInfo,
  displaySuccess,
  displayWarning,
  formatDuration,
  formatFileSize,
} from '../utils';

const logger = createLogger('BatchConvertCommand');

interface BatchConvertOptions {
  input?: string;
  output?: string;
  format?: 'jpg' | 'png' | 'svg';
  preset?: 'web' | 'print' | 'presentation' | 'poster';
  parallel?: number;
  organize?: boolean;
  recursive?: boolean;
  exclude?: string;
  dryRun?: boolean;
  force?: boolean;
  report?: boolean;
  reportFormat?: 'json' | 'csv' | 'html';
}

interface ProcessingStats {
  totalFiles: number;
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  totalSize: number;
  totalDuration: number;
  errors: Array<{ file: string; error: string }>;
}

export function batchConvertCommand(configManager: ConfigManager): Command {
  const command = new Command('batch-convert');

  command
    .description('Advanced batch conversion with parallel processing and organization')
    .option('-i, --input <pattern>', 'input pattern (e.g., "**/*.mmd", "diagrams/")', '**/*.mmd')
    .option('-o, --output <dir>', 'output directory', './converted')
    .option('-f, --format <format>', 'output format (jpg, png, svg)', 'png')
    .option('--preset <preset>', 'quality preset (web, print, presentation, poster)', 'presentation')
    .option('--parallel <number>', 'number of parallel conversions', '3')
    .option('--organize', 'organize output by format and date')
    .option('--recursive', 'search recursively in subdirectories')
    .option('--exclude <pattern>', 'exclude pattern (e.g., "**/temp/**")')
    .option('--dry-run', 'show what would be converted without actually converting')
    .option('--force', 'overwrite existing files without confirmation')
    .option('--report', 'generate conversion report')
    .option('--report-format <format>', 'report format (json, csv, html)', 'html')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architek-ai batch-convert -i "diagrams/**/*.mmd" -o ./images')}
  ${chalk.cyan('architek-ai batch-convert --organize --parallel 5 --preset poster')}
  ${chalk.cyan('architek-ai batch-convert --dry-run --report')}
  ${chalk.cyan('architek-ai batch-convert -i "*.mmd" --exclude "**/temp/**"')}

Features:
  • Parallel processing for faster conversion
  • Smart organization by format and date
  • Comprehensive progress tracking
  • Detailed conversion reports
  • Dry-run mode for testing
  • Exclude patterns for filtering
    `)
    .action(async (options: BatchConvertOptions) => {
      await executeBatchConvert(options, configManager);
    });

  return command;
}

async function executeBatchConvert(
  options: BatchConvertOptions,
  _configManager: ConfigManager
): Promise<void> {
  const startTime = Date.now();
  let spinner = createSpinner('Scanning for files...');
  
  try {
    spinner.start();

    // Find all matching files
    const files = await findMatchingFiles(options);
    
    if (files.length === 0) {
      spinner.fail('No files found');
      displayWarning(`No .mmd files found matching pattern: ${options.input}`);
      return;
    }

    spinner.stop();
    
    displayInfo(`Found ${files.length} file(s) to process`);
    
    if (options.dryRun) {
      displayInfo('\n📋 Dry Run - Files that would be converted:');
      files.forEach((file, index) => {
        const outputPath = generateOutputPath(file, options);
        displayInfo(`  ${index + 1}. ${file} → ${outputPath}`);
      });
      return;
    }

    // Initialize stats
    const stats: ProcessingStats = {
      totalFiles: files.length,
      processed: 0,
      successful: 0,
      failed: 0,
      skipped: 0,
      totalSize: 0,
      totalDuration: 0,
      errors: [],
    };

    // Process files in parallel batches
    const parallelCount = Number(options.parallel) || 3;
    const converter = new MermaidConverter();
    await converter.initialize();

    displayInfo(`\n🚀 Starting batch conversion with ${parallelCount} parallel workers...\n`);

    // Process in chunks
    for (let i = 0; i < files.length; i += parallelCount) {
      const chunk = files.slice(i, i + parallelCount);
      const promises = chunk.map(file => processFile(file, options, converter, stats));
      
      await Promise.allSettled(promises);
      
      // Update progress
      const progress = Math.round((stats.processed / stats.totalFiles) * 100);
      displayInfo(`Progress: ${stats.processed}/${stats.totalFiles} (${progress}%) - ✅ ${stats.successful} ❌ ${stats.failed} ⏭️ ${stats.skipped}`);
    }

    await converter.cleanup();

    // Display final results
    displayResults(stats, Date.now() - startTime);

    // Generate report if requested
    if (options.report) {
      await generateReport(stats, options);
    }

  } catch (error) {
    if (spinner) {
      spinner.fail('Batch conversion failed');
    }
    
    logger.error('Batch convert command failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function findMatchingFiles(options: BatchConvertOptions): Promise<string[]> {
  const pattern = options.input || '**/*.mmd';
  const globOptions: any = {
    ignore: ['node_modules/**', 'dist/**', '.git/**'],
    absolute: true,
  };

  if (options.exclude) {
    globOptions.ignore.push(options.exclude);
  }

  if (!options.recursive) {
    globOptions.ignore.push('**/*/**/*.mmd');
  }

  const files = await glob(pattern, globOptions);
  return files.filter(file => path.extname(file).toLowerCase() === '.mmd');
}

function generateOutputPath(inputFile: string, options: BatchConvertOptions): string {
  const outputDir = options.output || './converted';
  const format = options.format || 'png';
  const basename = path.basename(inputFile, '.mmd');
  
  if (options.organize) {
    const date = new Date().toISOString().split('T')[0];
    return path.join(outputDir, format, date!, `${basename}.${format}`);
  }
  
  return path.join(outputDir, `${basename}.${format}`);
}

async function processFile(
  inputFile: string,
  options: BatchConvertOptions,
  converter: MermaidConverter,
  stats: ProcessingStats
): Promise<void> {
  try {
    const outputPath = generateOutputPath(inputFile, options);
    
    // Check if output already exists
    if (!options.force && await fs.pathExists(outputPath)) {
      stats.skipped++;
      stats.processed++;
      displayWarning(`⏭️  Skipped (exists): ${path.basename(inputFile)}`);
      return;
    }

    // Ensure output directory exists
    await fs.ensureDir(path.dirname(outputPath));

    // Get quality preset
    const presets = {
      web: { width: 1920, height: 1080, scale: 1, quality: 85 },
      print: { width: 3300, height: 2550, scale: 2, quality: 95 },
      presentation: { width: 3840, height: 2160, scale: 2, quality: 95 },
      poster: { width: 7680, height: 4320, scale: 3, quality: 98 },
    };
    
    const preset = presets[options.preset || 'presentation'];

    // Convert file
    const conversionOptions: ConversionOptions = {
      format: options.format || 'png',
      ...preset,
      outputDir: path.dirname(outputPath),
      overwrite: true,
    };

    const result = await converter.convertFile(inputFile, conversionOptions);
    
    if (result.success) {
      stats.successful++;
      stats.totalSize += result.size;
      displaySuccess(`✅ ${path.basename(inputFile)} → ${formatFileSize(result.size)}`);
    } else {
      stats.failed++;
      stats.errors.push({ file: inputFile, error: result.error || 'Unknown error' });
      displayError(`❌ ${path.basename(inputFile)}: ${result.error}`);
    }

  } catch (error) {
    stats.failed++;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    stats.errors.push({ file: inputFile, error: errorMessage });
    displayError(`❌ ${path.basename(inputFile)}: ${errorMessage}`);
  } finally {
    stats.processed++;
  }
}

function displayResults(stats: ProcessingStats, totalDuration: number): void {
  displayInfo('\n📊 Batch Conversion Results:');
  displayInfo(`   Total Files: ${stats.totalFiles}`);
  displaySuccess(`   Successful: ${stats.successful}`);
  displayError(`   Failed: ${stats.failed}`);
  displayWarning(`   Skipped: ${stats.skipped}`);
  displayInfo(`   Total Size: ${formatFileSize(stats.totalSize)}`);
  displayInfo(`   Duration: ${formatDuration(totalDuration)}`);
  
  if (stats.successful > 0) {
    const avgTime = totalDuration / stats.successful;
    displayInfo(`   Avg Time/File: ${formatDuration(avgTime)}`);
  }

  if (stats.errors.length > 0) {
    displayInfo('\n❌ Errors:');
    stats.errors.forEach(error => {
      displayError(`   ${path.basename(error.file)}: ${error.error}`);
    });
  }
}

async function generateReport(stats: ProcessingStats, options: BatchConvertOptions): Promise<void> {
  const reportDir = './reports';
  await fs.ensureDir(reportDir);
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(reportDir, `batch-conversion-${timestamp}.${options.reportFormat || 'html'}`);
  
  let content = '';
  
  switch (options.reportFormat) {
    case 'json':
      content = JSON.stringify(stats, null, 2);
      break;
      
    case 'csv':
      content = 'File,Status,Error\n';
      content += stats.errors.map(e => `"${e.file}",Failed,"${e.error}"`).join('\n');
      break;
      
    case 'html':
    default:
      content = generateHTMLReport(stats);
      break;
  }
  
  await fs.writeFile(reportFile, content);
  displaySuccess(`📄 Report generated: ${reportFile}`);
}

function generateHTMLReport(stats: ProcessingStats): string {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Batch Conversion Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: white; border: 1px solid #dee2e6; padding: 15px; border-radius: 8px; text-align: center; }
        .errors { margin-top: 30px; }
        .error-item { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Batch Conversion Report</h1>
        <p>Generated on ${new Date().toLocaleString()}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <h3>${stats.totalFiles}</h3>
            <p>Total Files</p>
        </div>
        <div class="stat-card">
            <h3>${stats.successful}</h3>
            <p>Successful</p>
        </div>
        <div class="stat-card">
            <h3>${stats.failed}</h3>
            <p>Failed</p>
        </div>
        <div class="stat-card">
            <h3>${stats.skipped}</h3>
            <p>Skipped</p>
        </div>
    </div>
    
    ${stats.errors.length > 0 ? `
    <div class="errors">
        <h2>Errors</h2>
        ${stats.errors.map(error => `
            <div class="error-item">
                <strong>${path.basename(error.file)}</strong>: ${error.error}
            </div>
        `).join('')}
    </div>
    ` : ''}
</body>
</html>`;
}
