flowchart TD
    %% Input Processing
    UserQuery[💬 User Query] --> IntentAnalysis[🎯 Intent Analysis<br/>& Context Extraction]
    IntentAnalysis --> KnowledgeBoundary{📚 Within Knowledge Domain?}
    
    KnowledgeBoundary -->|No| OutOfScope[❌ No information<br/>about that topic]
    KnowledgeBoundary -->|Yes| RetrievalProcess[🔍 Information Retrieval]

    %% Retrieval & Verification
    RetrievalProcess --> DataSources{📊 Data Source Selection}
    DataSources --> StructuredData[🗄️ Structured Database<br/>Products, Orders, Inventory]
    DataSources --> VectorRetrieval[🧠 Vector Search<br/>Policies, FAQs, Guides]
    DataSources --> LiveAPI[📡 Real-time API<br/>Current Prices, Stock]

    %% Source Validation
    StructuredData --> SourceValidation[✅ Source Validation]
    VectorRetrieval --> SourceValidation
    LiveAPI --> SourceValidation
    
    SourceValidation --> ConfidenceScoring[📊 Confidence Scoring]
    ConfidenceScoring --> ConfidenceCheck{Confidence ≥ 85%?}
    
    ConfidenceCheck -->|No| LowConfidenceHandler[⚠️ Low Confidence Handler]
    ConfidenceCheck -->|Yes| ResponseGeneration[📝 Response Generation]

    %% Low Confidence Handling
    LowConfidenceHandler --> UncertaintyResponse[🤔 Not completely certain.<br/>Connect with human specialist.]
    LowConfidenceHandler --> FallbackSearch[🔄 Fallback: Broader Search]
    FallbackSearch --> ManualReview[👨‍💼 Queue for Manual Review]

    %% Response Generation with Constraints
    ResponseGeneration --> TemplateConstraints[📋 Template-Based Response<br/>with Structured Fields]
    TemplateConstraints --> FactualClaims[📋 Extract Factual Claims]
    
    FactualClaims --> ClaimVerification{🔍 Verify Each Claim}
    ClaimVerification -->|Verified| SourceAttribution[📝 Add Source Citations]
    ClaimVerification -->|Unverified| ClaimRemoval[❌ Remove Unverified Claims]
    ClaimVerification -->|Uncertain| QualifyStatement[⚠️ Add Uncertainty Qualifiers]

    %% Multi-Layer Quality Checks
    SourceAttribution --> QualityGates[🚪 Quality Gates]
    ClaimRemoval --> QualityGates
    QualifyStatement --> QualityGates
    
    QualityGates --> ContradictionCheck[🔍 Contradiction Detection]
    ContradictionCheck --> PolicyCompliance[📋 Policy Compliance Check]
    PolicyCompliance --> SafetyCheck[🛡️ Safety & Harm Prevention]
    
    SafetyCheck --> QADecision{✅ Pass All Checks?}
    QADecision -->|Pass| FinalResponse[🎯 Deliver Response with Citations]
    QADecision -->|Fail| BlockResponse[🚫 Block & Escalate]
    QADecision -->|Flag| FlaggedResponse[⚠️ Deliver with Strong Disclaimers]

    %% Response Structure Template
    subgraph ResponseTemplate ["📋 Response Template Structure"]
        ResponseStructure[🎯 Direct Answer<br/>📚 Source Citations<br/>📊 Confidence Level<br/>⚠️ Limitations/Disclaimers<br/>🔄 Last Updated Timestamp]
    end

    FinalResponse --> ResponseStructure

    %% Feedback Loop & Learning
    FinalResponse --> FeedbackCapture[📝 Capture User Feedback]
    BlockResponse --> ErrorLogging[📊 Log Blocked Response]
    FlaggedResponse --> FeedbackCapture
    
    FeedbackCapture --> LearningLoop[🧠 Continuous Learning]
    ErrorLogging --> LearningLoop
    LearningLoop --> ModelTuning[⚙️ Fine-tune Detection Models]
    
    %% Real-time Monitoring
    QualityGates --> MonitoringDashboard[📊 Real-time Monitoring]
    MonitoringDashboard --> HallucinationMetrics[📈 Hallucination Rate Tracking]
    MonitoringDashboard --> AccuracyMetrics[🎯 Response Accuracy Metrics]
    MonitoringDashboard --> UserSatisfaction[😊 User Satisfaction Scores]

    %% Alert System
    HallucinationMetrics --> AlertThresholds{🚨 Above Alert Threshold?}
    AlertThresholds -->|Yes| ImmediateAlert[📱 Immediate Alert to Team]
    AlertThresholds -->|No| ContinuousMonitoring[👁️ Continue Monitoring]

    %% Emergency Fallback
    ImmediateAlert --> EmergencyFallback[🆘 Emergency Fallback Mode]
    EmergencyFallback --> HumanHandoff[👨‍💼 All Queries → Human Agents]

    %% Styling
    classDef prevention fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef validation fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef processing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef alert fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class KnowledgeBoundary,ConfidenceCheck,ClaimVerification,QADecision prevention
    class SourceValidation,ContradictionCheck,PolicyCompliance,SafetyCheck validation
    class IntentAnalysis,RetrievalProcess,ResponseGeneration,FactualClaims processing
    class AlertThresholds,ImmediateAlert,EmergencyFallback alert