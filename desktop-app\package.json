{"name": "mermaid-converter-desktop", "version": "1.0.0", "description": "Desktop application for converting Mermaid diagrams to images", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "npm run build && electron dist/main.js", "dev": "tsc && electron dist/main.js --dev", "pack": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["mermaid", "diagram", "converter", "electron", "desktop"], "author": "ArchitekAI", "license": "MIT", "devDependencies": {"@types/node": "^20.8.10", "electron": "^27.1.3", "electron-builder": "^24.6.4", "rimraf": "^5.0.5", "typescript": "^5.2.2"}, "dependencies": {"puppeteer": "^23.11.1", "sharp": "^0.34.3", "fs-extra": "^11.1.1", "glob": "^10.3.10"}, "build": {"appId": "com.architekAI.mermaid-converter", "productName": "Mermaid Converter", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}}}