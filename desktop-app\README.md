# Mermaid Converter Desktop Application

A cross-platform desktop application for converting Mermaid diagrams to high-quality images with batch processing capabilities.

## Features

- **GUI Interface**: User-friendly desktop application with drag-and-drop support
- **Batch Processing**: Convert multiple .mmd files simultaneously with progress tracking
- **Multiple Formats**: Export to PNG, JPEG, SVG formats
- **Quality Control**: Configurable quality settings and output dimensions
- **Theme Support**: Multiple Mermaid themes (default, dark, forest, neutral)
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Progress Tracking**: Real-time conversion progress with detailed status
- **Error Handling**: Comprehensive error reporting and retry capabilities

## Installation

### Prerequisites

- Node.js 18+ 
- npm or yarn package manager

### Setup

1. **Install Dependencies**
   ```bash
   cd desktop-app
   npm install
   ```

2. **Build the Application**
   ```bash
   npm run build
   ```

3. **Run in Development Mode**
   ```bash
   npm run dev
   ```

4. **Create Distribution Package**
   ```bash
   npm run dist
   ```

## Usage

### Basic Conversion

1. **Launch the Application**
   - Run `npm start` or use the built executable

2. **Configure Settings**
   - Select output format (PNG, JPEG, SVG)
   - Adjust quality settings (1-100%)
   - Choose Mermaid theme
   - Select output folder

3. **Select Files**
   - Click "Select Individual Files" to choose specific .mmd files
   - Or click "Select Folder" to process all .mmd files in a directory

4. **Start Conversion**
   - Click "Convert All" to begin batch processing
   - Monitor progress in real-time
   - View results and open output folder when complete

### Advanced Features

#### Quality Presets
- **Web (90%)**: Optimized for web display
- **Print (95%)**: High quality for printing
- **Presentation (98%)**: Maximum quality for presentations

#### Batch Processing
- Processes up to 3 files simultaneously for optimal performance
- Real-time progress tracking for each file
- Automatic error handling and retry logic
- Bulk operations for large file sets

#### Output Management
- Configurable output directory
- Automatic file naming with format extension
- Option to open output folder after conversion
- Detailed conversion results with file sizes and dimensions

## File Structure

```
desktop-app/
├── src/
│   ├── main.ts              # Electron main process
│   ├── preload.ts           # Preload script for IPC
│   └── converter/
│       └── MermaidConverter.ts  # Core conversion logic
├── assets/
│   └── index.html           # Application UI
├── dist/                    # Compiled TypeScript
├── release/                 # Built application packages
├── package.json
├── tsconfig.json
└── README.md
```

## Development

### Building from Source

1. **Clone and Setup**
   ```bash
   git clone <repository>
   cd desktop-app
   npm install
   ```

2. **Development Mode**
   ```bash
   npm run dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   npm run dist
   ```

### Architecture

- **Electron Main Process**: Handles file system operations, conversion logic
- **Renderer Process**: UI interface built with HTML/CSS/JavaScript
- **IPC Communication**: Secure communication between main and renderer processes
- **Puppeteer Integration**: Headless browser for Mermaid rendering
- **Sharp Processing**: Image optimization and format conversion

### API Reference

#### Main Process APIs

```typescript
// File selection
electronAPI.selectFiles(): Promise<{canceled: boolean, files?: File[]}>
electronAPI.selectFolder(): Promise<{canceled: boolean, path?: string}>

// Conversion
electronAPI.convertFile(fileData, options): Promise<ConversionResult>
electronAPI.convertBatch(files, options): Promise<ConversionResult[]>

// Utilities
electronAPI.openOutputFolder(path): Promise<void>
electronAPI.getAppInfo(): Promise<AppInfo>
```

#### Conversion Options

```typescript
interface ConversionOptions {
  format: 'jpg' | 'jpeg' | 'png' | 'svg';
  quality?: number;           // 1-100
  width?: number;            // Output width
  height?: number;           // Output height
  theme?: string;            // Mermaid theme
  backgroundColor?: string;   // Background color
  outputDir?: string;        // Output directory
  outputFileName?: string;   // Custom filename
}
```

## Troubleshooting

### Common Issues

1. **Conversion Fails**
   - Verify .mmd file syntax is valid
   - Check file permissions in output directory
   - Ensure sufficient disk space

2. **Performance Issues**
   - Reduce batch size for large files
   - Close other applications to free memory
   - Use SSD storage for better I/O performance

3. **Installation Problems**
   - Update Node.js to latest LTS version
   - Clear npm cache: `npm cache clean --force`
   - Reinstall dependencies: `rm -rf node_modules && npm install`

### Error Codes

- `CONVERTER_NOT_INITIALIZED`: Puppeteer browser failed to start
- `INVALID_MERMAID_SYNTAX`: Mermaid diagram syntax error
- `FILE_WRITE_ERROR`: Cannot write to output directory
- `MEMORY_ERROR`: Insufficient memory for conversion

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the Mermaid documentation for syntax help
