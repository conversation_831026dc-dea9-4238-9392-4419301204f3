import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { MermaidPreview } from '../components/MermaidPreview';
import { ExportButton } from '../components/ExportButton';
import { FileUpload } from '../components/FileUpload';
import { DiagramGallery } from '../components/DiagramGallery';
import { useTheme } from '../hooks/useTheme';

const GeneratePage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { resolvedTheme } = useTheme();

  const [description, setDescription] = useState('');
  const [format, setFormat] = useState('mermaid');
  const [outputFormat, setOutputFormat] = useState<'mermaid' | 'jpg' | 'png' | 'svg'>('mermaid');
  const [template, setTemplate] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [resultBlob, setResultBlob] = useState<Blob | null>(null);
  const [activeTab, setActiveTab] = useState<'generate' | 'upload' | 'gallery'>('generate');
  const [diagramTheme, setDiagramTheme] = useState<'default' | 'dark' | 'forest' | 'neutral'>('default');

  // Initialize from URL parameters
  useEffect(() => {
    const templateParam = searchParams.get('template');
    const descriptionParam = searchParams.get('description');

    if (templateParam) {
      setTemplate(templateParam);
    }

    if (descriptionParam) {
      setDescription(descriptionParam);
    }
  }, [searchParams]);
  const [livePreview, setLivePreview] = useState(true);

  const convertToFormat = async (mermaidContent: string, targetFormat: 'jpg' | 'png' | 'svg') => {
    try {
      const response = await fetch('/api/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: mermaidContent,
          format: targetFormat,
          options: {
            quality: 95,
            width: 1920,
            height: 1080,
            scale: 2,
            theme: diagramTheme,
            backgroundColor: '#ffffff',
            qualityPreset: 'web',
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Conversion failed: ${response.statusText}`);
      }

      const blob = await response.blob();
      setResultBlob(blob);
    } catch (error) {
      console.error('Conversion failed:', error);
      alert(error instanceof Error ? error.message : 'Conversion failed. Please try again.');
    }
  };

  const handleGenerate = async () => {
    if (!description.trim()) {
      alert('Please enter a description');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description,
          format,
          template: template || undefined,
          options: {
            interactive: false,
            validate: true,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Generation failed: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        const mermaidContent = data.data.content;
        setResult(mermaidContent);

        // If output format is not mermaid, convert to the desired format
        if (outputFormat !== 'mermaid') {
          await convertToFormat(mermaidContent, outputFormat);
        } else {
          setResultBlob(null);
        }

        // Save to gallery
        saveDiagramToGallery(mermaidContent, description);
      } else {
        throw new Error(data.error || 'Generation failed');
      }
    } catch (error) {
      console.error('Generation failed:', error);
      alert(error instanceof Error ? error.message : 'Generation failed. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const saveDiagramToGallery = (content: string, desc: string) => {
    try {
      const savedDiagrams = localStorage.getItem('architek-ai-diagrams');
      const diagrams = savedDiagrams ? JSON.parse(savedDiagrams) : [];

      const newDiagram = {
        id: `diagram-${Date.now()}`,
        name: `Generated Diagram ${diagrams.length + 1}`,
        content,
        description: desc,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: [format, template].filter(Boolean),
      };

      diagrams.unshift(newDiagram);
      localStorage.setItem('architek-ai-diagrams', JSON.stringify(diagrams));
    } catch (error) {
      console.warn('Failed to save diagram to gallery:', error);
    }
  };

  const handleFileUpload = (file: File, content: string) => {
    setResult(content);
    setDescription(`Uploaded from ${file.name}`);
    saveDiagramToGallery(content, `Uploaded from ${file.name}`);
  };

  const handleGallerySelect = (diagram: any) => {
    setResult(diagram.content);
    setDescription(diagram.description || diagram.name);
    setActiveTab('generate');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Architecture Diagram Studio</h1>

          {/* Tab Navigation */}
          <div className="mb-8">
            <nav className="flex space-x-8">
              {[
                { id: 'generate', label: 'Generate', icon: '✨' },
                { id: 'upload', label: 'Upload', icon: '📁' },
                { id: 'gallery', label: 'Gallery', icon: '🖼️' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Generate Tab */}
          {activeTab === 'generate' && (
            <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Describe Your Architecture</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Architecture Description
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => {
                      setDescription(e.target.value);
                      // Live preview for Mermaid content
                      if (livePreview && format === 'mermaid' && e.target.value.trim().startsWith('graph')) {
                        setResult(e.target.value);
                      }
                    }}
                    placeholder="e.g., microservices architecture with API gateway, user service, and payment service"
                    className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Output Format
                    </label>
                    <select
                      value={format}
                      onChange={(e) => setFormat(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="mermaid">Mermaid</option>
                      <option value="plantuml">PlantUML</option>
                      <option value="ascii">ASCII</option>
                      <option value="drawio">Draw.io</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Theme
                    </label>
                    <select
                      value={diagramTheme}
                      onChange={(e) => setDiagramTheme(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="default">Default</option>
                      <option value="dark">Dark</option>
                      <option value="forest">Forest</option>
                      <option value="neutral">Neutral</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Output Format
                  </label>
                  <select
                    value={outputFormat}
                    onChange={(e) => setOutputFormat(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="mermaid">Mermaid Code</option>
                    <option value="png">PNG Image</option>
                    <option value="jpg">JPEG Image</option>
                    <option value="svg">SVG Vector</option>
                  </select>
                  <p className="text-sm text-gray-500 mt-1">
                    {outputFormat === 'mermaid'
                      ? 'Generate Mermaid diagram code for editing'
                      : `Generate high-quality ${outputFormat.toUpperCase()} image ready for download`
                    }
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Template (Optional)
                  </label>
                  <select
                    value={template}
                    onChange={(e) => setTemplate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">No Template</option>
                    <option value="microservices">Microservices</option>
                    <option value="monolithic">Monolithic</option>
                    <option value="serverless">Serverless</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="livePreview"
                    checked={livePreview}
                    onChange={(e) => setLivePreview(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="livePreview" className="ml-2 block text-sm text-gray-700">
                    Enable live preview (for Mermaid syntax)
                  </label>
                </div>

                <button
                  onClick={handleGenerate}
                  disabled={isGenerating || !description.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-semibold transition-colors"
                >
                  {isGenerating ? 'Generating...' : 'Generate Diagram'}
                </button>
              </div>
            </div>
            
            {/* Output Section */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Generated Diagram</h2>
                {result && outputFormat === 'mermaid' && (
                  <ExportButton
                    content={result}
                    filename="architecture-diagram"
                    className="ml-4"
                  />
                )}
                {resultBlob && outputFormat !== 'mermaid' && (
                  <button
                    onClick={() => {
                      const url = URL.createObjectURL(resultBlob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `architecture-diagram.${outputFormat}`;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-semibold transition-colors"
                  >
                    Download {outputFormat.toUpperCase()}
                  </button>
                )}
              </div>
              
              {result || resultBlob ? (
                <div className="space-y-4">
                  {/* Image Preview for non-Mermaid formats */}
                  {resultBlob && outputFormat !== 'mermaid' && (
                    <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Generated {outputFormat.toUpperCase()} Image</h3>
                      <div className="bg-white rounded border p-4 text-center">
                        <img
                          src={URL.createObjectURL(resultBlob)}
                          alt="Generated diagram"
                          className="max-w-full h-auto mx-auto"
                          style={{ maxHeight: '500px' }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Live Preview for Mermaid */}
                  {result && outputFormat === 'mermaid' && format === 'mermaid' && (
                    <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Live Preview</h3>
                      <MermaidPreview
                        content={result}
                        theme={resolvedTheme === 'dark' ? 'dark' : diagramTheme}
                        className="bg-white rounded border"
                      />
                    </div>
                  )}

                  {/* Code View for Mermaid */}
                  {result && outputFormat === 'mermaid' && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Generated Code</h3>
                      <div className="bg-gray-100 p-4 rounded-lg">
                        <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                          {result}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Action buttons for Mermaid code */}
                  {result && outputFormat === 'mermaid' && (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => navigator.clipboard.writeText(result)}
                        className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                      >
                        Copy Code
                      </button>
                      <button
                        onClick={() => {
                          const blob = new Blob([result], { type: 'text/plain' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = 'architecture.mmd';
                          a.click();
                          URL.revokeObjectURL(url);
                        }}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                      >
                        Download .mmd
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <div className="text-4xl mb-4">📊</div>
                  <p>Your generated diagram will appear here</p>
                  <p className="text-sm mt-2">Enter a description and click "Generate Diagram" to get started</p>
                </div>
              )}
            </div>
          </div>
          )}

          {/* Upload Tab */}
          {activeTab === 'upload' && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Upload Mermaid Files</h2>
                <FileUpload
                  onFileSelect={handleFileUpload}
                  onError={(error) => alert(error)}
                />

                {result && (
                  <div className="mt-8">
                    <h3 className="text-lg font-medium mb-4">Uploaded Diagram</h3>
                    <div className="grid lg:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Preview</h4>
                        <MermaidPreview
                          content={result}
                          theme={resolvedTheme === 'dark' ? 'dark' : diagramTheme}
                          className="border border-gray-200 rounded-lg p-4"
                        />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-gray-700">Code</h4>
                          <ExportButton
                            content={result}
                            filename="uploaded-diagram"
                          />
                        </div>
                        <div className="bg-gray-100 p-4 rounded-lg">
                          <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                            {result}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Gallery Tab */}
          {activeTab === 'gallery' && (
            <DiagramGallery
              onSelectDiagram={handleGallerySelect}
              onDeleteDiagram={(_id) => {
                // Refresh gallery after deletion
                window.location.reload();
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default GeneratePage;
