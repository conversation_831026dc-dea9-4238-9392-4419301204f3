#!/usr/bin/env node

/**
 * Test script to verify image quality improvements
 * Tests various quality presets and formats
 */

const fs = require('fs-extra');
const path = require('path');

// Create test Mermaid diagram
const testDiagram = `
graph TD
    A[User] --> B[API Gateway]
    B --> C[Authentication Service]
    B --> D[User Service]
    B --> E[Order Service]
    C --> F[User Database]
    D --> F
    E --> G[Order Database]
    E --> H[Payment Service]
    H --> I[Payment Gateway]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#e8f5e8
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#fce4ec
    style I fill:#f1f8e9
`;

async function testImageQuality() {
  console.log('🧪 Testing Image Quality Improvements...\n');
  
  try {
    // Create test directory
    const testDir = path.join(__dirname, 'quality-test-output');
    await fs.ensureDir(testDir);
    
    // Create test mermaid file
    const testFile = path.join(testDir, 'test-diagram.mmd');
    await fs.writeFile(testFile, testDiagram);
    
    console.log('✅ Created test diagram file');
    
    // Import the converter
    const { MermaidConverter } = require('./dist/core/converter');
    
    const converter = new MermaidConverter();
    await converter.initialize();
    
    console.log('✅ Initialized converter');
    
    // Test different quality presets
    const presets = ['web', 'print', 'presentation', 'poster', 'ultra-high'];
    const formats = ['png', 'jpg'];
    
    for (const preset of presets) {
      for (const format of formats) {
        console.log(`\n🔄 Testing ${preset} preset with ${format} format...`);
        
        const outputFile = path.join(testDir, `test-${preset}.${format}`);
        
        const result = await converter.convertFile(testFile, {
          format,
          qualityPreset: preset,
          outputDir: testDir,
          overwrite: true
        });
        
        if (result.success) {
          const stats = await fs.stat(result.outputFile);
          console.log(`   ✅ Generated: ${path.basename(result.outputFile)}`);
          console.log(`   📏 Dimensions: ${result.width}x${result.height}`);
          console.log(`   📦 Size: ${(stats.size / 1024).toFixed(1)} KB`);
          console.log(`   ⏱️  Duration: ${result.duration}ms`);
        } else {
          console.log(`   ❌ Failed: ${result.error}`);
        }
      }
    }
    
    // Test custom high-quality settings
    console.log(`\n🔄 Testing custom high-quality settings...`);
    
    const customResult = await converter.convertFile(testFile, {
      format: 'png',
      width: 2560,
      height: 1440,
      scale: 3,
      quality: 100,
      outputDir: testDir,
      overwrite: true
    });
    
    if (customResult.success) {
      const stats = await fs.stat(customResult.outputFile);
      console.log(`   ✅ Generated: ${path.basename(customResult.outputFile)}`);
      console.log(`   📏 Dimensions: ${customResult.width}x${customResult.height}`);
      console.log(`   📦 Size: ${(stats.size / 1024).toFixed(1)} KB`);
      console.log(`   ⏱️  Duration: ${customResult.duration}ms`);
    }
    
    await converter.cleanup();
    
    console.log(`\n🎉 Quality test completed! Check output files in: ${testDir}`);
    console.log('\n📋 Quality Assessment Tips:');
    console.log('   • Open images at 100% zoom to check for pixelation');
    console.log('   • Compare file sizes - higher quality should be larger');
    console.log('   • Text should be crisp and readable at all sizes');
    console.log('   • Lines and shapes should have smooth edges');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testImageQuality();
}

module.exports = { testImageQuality };
