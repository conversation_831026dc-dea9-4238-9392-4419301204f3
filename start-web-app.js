#!/usr/bin/env node

/**
 * ArchitekAI Web Application Launcher
 * 
 * This script starts both the API server and frontend development server
 * for the ArchitekAI web application.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting ArchitekAI Web Application...\n');

// Check if we're in the correct directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root directory.');
  process.exit(1);
}

// Function to start a process with colored output
function startProcess(name, command, args, cwd, color) {
  const colorCodes = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };

  console.log(`${colorCodes[color]}🔧 Starting ${name}...${colorCodes.reset}`);
  
  const proc = spawn(command, args, {
    cwd: cwd || process.cwd(),
    stdio: 'pipe',
    shell: true
  });

  proc.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log(`${colorCodes[color]}[${name}]${colorCodes.reset} ${line}`);
    });
  });

  proc.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log(`${colorCodes.red}[${name} ERROR]${colorCodes.reset} ${line}`);
    });
  });

  proc.on('close', (code) => {
    console.log(`${colorCodes[color]}[${name}]${colorCodes.reset} Process exited with code ${code}`);
  });

  return proc;
}

// Start API server (mock server for now)
console.log('📡 Starting API Server on http://localhost:3000');
const apiServer = startProcess(
  'API Server',
  'node',
  ['src/web/api/mock-server.js'],
  process.cwd(),
  'blue'
);

// Wait a moment for API server to start
setTimeout(() => {
  console.log('🌐 Starting Frontend Server on http://localhost:3001');
  const frontendServer = startProcess(
    'Frontend',
    'npm',
    ['run', 'dev'],
    path.join(process.cwd(), 'src', 'web', 'frontend'),
    'green'
  );

  // Wait for frontend to start, then show success message
  setTimeout(() => {
    console.log('\n✅ ArchitekAI Web Application is now running!');
    console.log('\n📋 Available URLs:');
    console.log('   🌐 Web Interface: http://localhost:3001');
    console.log('   📡 API Server:    http://localhost:3000');
    console.log('   🔍 API Health:    http://localhost:3000/health');
    console.log('\n🎯 Quick Start:');
    console.log('   1. Open http://localhost:3001 in your browser');
    console.log('   2. Click "Generate" in the sidebar');
    console.log('   3. Enter your architecture description');
    console.log('   4. Click "Generate Diagram" to create your diagram');
    console.log('\n⏹️  Press Ctrl+C to stop both servers');
  }, 3000);

}, 2000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down ArchitekAI Web Application...');
  apiServer.kill();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down ArchitekAI Web Application...');
  apiServer.kill();
  process.exit(0);
});
