import React from 'react';
import { Link, useLocation } from 'react-router-dom';

interface SidebarProps {
  open?: boolean;
  onClose?: () => void;
  mobile?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ open = true, onClose, mobile = false }) => {
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/', icon: '🏠' },
    { name: 'Generate', href: '/generate', icon: '⚡' },
    { name: 'Batch Convert', href: '/batch-convert', icon: '🔄' },
    { name: 'Templates', href: '/templates', icon: '📋' },
    { name: 'Examples', href: '/examples', icon: '📊' },
    { name: 'Documentation', href: '/docs', icon: '📚' },
    { name: 'Settings', href: '/settings', icon: '⚙️' },
  ];

  const sidebarClasses = mobile
    ? `fixed inset-0 z-50 lg:hidden ${open ? 'block' : 'hidden'}`
    : 'flex h-full flex-col bg-white dark:bg-gray-800 shadow-sm';

  return (
    <>
      {mobile && open && (
        <div className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75" onClick={onClose} />
      )}
      
      <div className={sidebarClasses}>
        {mobile && (
          <div className="flex h-full flex-col bg-white dark:bg-gray-800 shadow-sm w-64">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">ArchitekAI</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ✕
              </button>
            </div>
            <SidebarContent navigation={navigation} currentPath={location.pathname} onItemClick={onClose} />
          </div>
        )}
        
        {!mobile && (
          <>
            <div className="flex items-center p-4 border-b">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">ArchitekAI</h2>
            </div>
            <SidebarContent navigation={navigation} currentPath={location.pathname} />
          </>
        )}
      </div>
    </>
  );
};

interface SidebarContentProps {
  navigation: Array<{ name: string; href: string; icon: string }>;
  currentPath: string;
  onItemClick?: (() => void) | undefined;
}

const SidebarContent: React.FC<SidebarContentProps> = ({ navigation, currentPath, onItemClick }) => {
  return (
    <nav className="flex-1 p-4 space-y-2">
      {navigation.map((item) => {
        const isActive = currentPath === item.href;
        return (
          <Link
            key={item.name}
            to={item.href}
            onClick={onItemClick}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              isActive
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
            }`}
          >
            <span className="mr-3 text-lg">{item.icon}</span>
            {item.name}
          </Link>
        );
      })}
    </nav>
  );
};

export default Sidebar;
