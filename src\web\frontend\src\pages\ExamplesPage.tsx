import React, { useState } from 'react';
import { MermaidPreview } from '../components/MermaidPreview';
import { ExportButton } from '../components/ExportButton';
import { useTheme } from '../hooks/useTheme';

const ExamplesPage: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [_selectedExample, _setSelectedExample] = useState<number | null>(null);
  const examples = [
    {
      title: 'E-commerce Platform',
      description: 'microservices architecture with API gateway, user service, order service, payment service, and notification service',
      diagram: `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Order Service]
    A --> D[Payment Service]
    B --> E[(User DB)]
    C --> F[(Order DB)]
    D --> G[(Payment DB)]
    C --> H[Notification Service]`
    },
    {
      title: 'Serverless Web App',
      description: 'serverless architecture with CloudFront, API Gateway, Lambda functions, and DynamoDB',
      diagram: `graph TD
    A[CloudFront] --> B[API Gateway]
    B --> C[Auth Function]
    B --> D[User Function]
    B --> E[Data Function]
    C --> F[(DynamoDB)]
    D --> F
    E --> F`
    },
    {
      title: 'Monolithic Application',
      description: 'traditional monolithic architecture with web server, application server, and database',
      diagram: `graph TD
    A[Load Balancer] --> B[Web Server]
    B --> C[Application Server]
    C --> D[(Database)]
    C --> E[Cache]`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Example Diagrams</h1>
          <p className="text-gray-600 mb-8">
            Explore these example architecture diagrams to see what ArchitekAI can generate.
          </p>
          
          <div className="space-y-8">
            {examples.map((example, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{example.title}</h3>
                
                <div className="grid lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Input Description:</h4>
                    <div className="bg-gray-100 p-3 rounded-lg text-sm font-mono">
                      "{example.description}"
                    </div>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Generated Mermaid Code:</h4>
                      <div className="bg-gray-100 p-3 rounded-lg">
                        <pre className="text-xs font-mono whitespace-pre-wrap">{example.diagram}</pre>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-700">Live Preview:</h4>
                      <ExportButton
                        content={example.diagram}
                        filename={`example-${example.title.toLowerCase().replace(/\s+/g, '-')}`}
                        className="scale-75"
                      />
                    </div>
                    <div className="border border-gray-200 rounded-lg p-4 bg-white">
                      <MermaidPreview
                        content={example.diagram}
                        theme={resolvedTheme === 'dark' ? 'dark' : 'default'}
                        className="min-h-[200px]"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 flex flex-wrap gap-2">
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(example.description);
                      // You could add a toast notification here
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                  >
                    Copy Description
                  </button>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(example.diagram);
                      // You could add a toast notification here
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                  >
                    Copy Diagram Code
                  </button>
                  <button
                    onClick={() => {
                      // Navigate to generate page with this example
                      const params = new URLSearchParams();
                      params.set('description', example.description);
                      window.location.href = `/generate?${params.toString()}`;
                    }}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                  >
                    Try This Example
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamplesPage;
