import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';

// Layout components
import Layout from './components/Layout/Layout';
import ErrorFallback from './components/ErrorFallback/ErrorFallback';

// Page components
import HomePage from './pages/HomePage';
import GeneratePage from './pages/GeneratePage';
import BatchConvertPage from './pages/BatchConvertPage';
import TemplatesPage from './pages/TemplatesPage';
import TemplateDetailPage from './pages/TemplateDetailPage';
import ExamplesPage from './pages/ExamplesPage';
import DocsPage from './pages/DocsPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';

// Hooks
import { useTheme } from './hooks/useTheme';

function App() {
  const { theme } = useTheme();

  React.useEffect(() => {
    // Apply theme to document
    document.documentElement.classList.toggle('dark', theme === 'dark');
  }, [theme]);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Application error:', error, errorInfo);
        // Here you could send error to monitoring service
      }}
    >
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="generate" element={<GeneratePage />} />
            <Route path="batch-convert" element={<BatchConvertPage />} />
            <Route path="templates" element={<TemplatesPage />} />
            <Route path="templates/:id" element={<TemplateDetailPage />} />
            <Route path="examples" element={<ExamplesPage />} />
            <Route path="docs" element={<DocsPage />} />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="*" element={<NotFoundPage />} />
          </Route>
        </Routes>
      </div>
    </ErrorBoundary>
  );
}

export default App;
