import { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';
import axios from 'axios';
import { MermaidConverter } from './converter/MermaidConverter';

class MermaidConverterApp {
  private mainWindow: BrowserWindow | null = null;
  private converter: MermaidConverter | null = null;

  constructor() {
    this.initializeApp();
  }

  private initializeApp(): void {
    // Handle app ready
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupIpcHandlers();
      this.initializeConverter();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Handle app window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup();
        app.quit();
      }
    });

    // Handle app before quit
    app.on('before-quit', () => {
      this.cleanup();
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
      },
      titleBarStyle: 'default',
      show: false, // Don't show until ready
    });

    // Load the HTML file
    this.mainWindow.loadFile(path.join(__dirname, '../assets/index.html'));

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      // Open DevTools in development
      if (process.argv.includes('--dev')) {
        this.mainWindow?.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private async initializeConverter(): Promise<void> {
    try {
      this.converter = new MermaidConverter();
      await this.converter.initialize();
      console.log('Mermaid converter initialized');
    } catch (error) {
      console.error('Failed to initialize converter:', error);
    }
  }

  private setupIpcHandlers(): void {
    // Handle file selection
    ipcMain.handle('select-files', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openFile', 'multiSelections'],
        filters: [
          { name: 'Mermaid Files', extensions: ['mmd'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (result.canceled) {
        return { canceled: true };
      }

      // Read file contents
      const files = await Promise.all(
        result.filePaths.map(async (filePath) => {
          const content = await fs.readFile(filePath, 'utf8');
          const stats = await fs.stat(filePath);
          return {
            path: filePath,
            name: path.basename(filePath),
            content,
            size: stats.size,
          };
        })
      );

      return { canceled: false, files };
    });

    // Handle folder selection
    ipcMain.handle('select-folder', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory']
      });

      if (result.canceled) {
        return { canceled: true };
      }

      return { canceled: false, path: result.filePaths[0] };
    });

    // Handle output folder selection
    ipcMain.handle('select-output-folder', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory', 'createDirectory']
      });

      if (result.canceled) {
        return { canceled: true };
      }

      return { canceled: false, path: result.filePaths[0] };
    });

    // Handle file conversion
    ipcMain.handle('convert-file', async (event, fileData, options) => {
      if (!this.converter) {
        throw new Error('Converter not initialized');
      }

      try {
        // Create temporary file
        const tempDir = path.join(__dirname, '../temp');
        await fs.ensureDir(tempDir);
        
        const tempFilePath = path.join(tempDir, `temp-${Date.now()}.mmd`);
        await fs.writeFile(tempFilePath, fileData.content);

        // Convert file
        const result = await this.converter.convertFile(tempFilePath, options);

        // Cleanup temp file
        await fs.remove(tempFilePath);

        return result;
      } catch (error) {
        throw error;
      }
    });

    // Handle batch conversion
    ipcMain.handle('convert-batch', async (event, files, options) => {
      if (!this.converter) {
        throw new Error('Converter not initialized');
      }

      const results = [];
      const tempDir = path.join(__dirname, '../temp');
      await fs.ensureDir(tempDir);

      for (const file of files) {
        try {
          // Create temporary file
          const tempFilePath = path.join(tempDir, `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.mmd`);
          await fs.writeFile(tempFilePath, file.content);

          // Convert file
          const result = await this.converter.convertFile(tempFilePath, {
            ...options,
            outputFileName: file.name.replace('.mmd', ''),
          });

          results.push({
            originalFile: file.name,
            success: result.success,
            outputPath: result.outputFile,
            error: result.error,
          });

          // Cleanup temp file
          await fs.remove(tempFilePath);

          // Send progress update
          this.mainWindow?.webContents.send('conversion-progress', {
            completed: results.length,
            total: files.length,
            currentFile: file.name,
          });

        } catch (error) {
          results.push({
            originalFile: file.name,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      return results;
    });

    // Handle opening output folder
    ipcMain.handle('open-output-folder', async (event, folderPath) => {
      await shell.openPath(folderPath);
    });

    // Handle AI diagram generation
    ipcMain.handle('generate-ai-diagram', async (event, options: {
      description: string;
      outputFormat: 'mermaid' | 'png' | 'jpg' | 'svg';
      template?: string;
      theme: string;
    }) => {
      try {
        // First, generate Mermaid code using the AI API
        const generateResponse = await axios.post('http://localhost:3000/api/generate', {
          description: options.description,
          format: 'mermaid',
          template: options.template,
          options: {
            interactive: false,
            validate: true,
          },
        });

        const generateData = generateResponse.data;

        if (!generateData.success || !generateData.data) {
          throw new Error(generateData.error || 'Generation failed');
        }

        const mermaidContent = generateData.data.content;

        // If output format is mermaid, return the code
        if (options.outputFormat === 'mermaid') {
          return {
            success: true,
            content: mermaidContent,
            outputFormat: 'mermaid'
          };
        }

        // Otherwise, convert to the desired format
        const convertResponse = await axios.post('http://localhost:3000/api/convert', {
          content: mermaidContent,
          format: options.outputFormat,
          options: {
            quality: 95,
            width: 1920,
            height: 1080,
            scale: 2,
            theme: options.theme,
            backgroundColor: '#ffffff',
            qualityPreset: 'web',
          },
        }, {
          responseType: 'arraybuffer'
        });

        const imageBuffer = Buffer.from(convertResponse.data);
        const imageData = imageBuffer.toString('base64');

        return {
          success: true,
          content: mermaidContent,
          imageData,
          outputFormat: options.outputFormat
        };

      } catch (error) {
        console.error('AI generation failed:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // Handle saving AI result
    ipcMain.handle('save-ai-result', async (event, result: {
      content: string;
      imageData?: string;
      outputFormat: string;
    }) => {
      try {
        const extension = result.outputFormat === 'mermaid' ? 'mmd' : result.outputFormat;
        const defaultPath = `architecture-diagram.${extension}`;

        const saveResult = await dialog.showSaveDialog(this.mainWindow!, {
          defaultPath,
          filters: [
            result.outputFormat === 'mermaid'
              ? { name: 'Mermaid Files', extensions: ['mmd'] }
              : { name: `${result.outputFormat.toUpperCase()} Files`, extensions: [result.outputFormat] }
          ]
        });

        if (saveResult.canceled || !saveResult.filePath) {
          return { success: false, error: 'Save cancelled' };
        }

        if (result.outputFormat === 'mermaid') {
          await fs.writeFile(saveResult.filePath, result.content, 'utf8');
        } else {
          const buffer = Buffer.from(result.imageData!, 'base64');
          await fs.writeFile(saveResult.filePath, buffer);
        }

        return { success: true, filePath: saveResult.filePath };

      } catch (error) {
        console.error('Save failed:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // Handle app info
    ipcMain.handle('get-app-info', () => {
      return {
        name: app.getName(),
        version: app.getVersion(),
        platform: process.platform,
      };
    });
  }

  private async cleanup(): Promise<void> {
    if (this.converter) {
      await this.converter.cleanup();
      this.converter = null;
    }

    // Clean up temp directory
    const tempDir = path.join(__dirname, '../temp');
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  }
}

// Create app instance
new MermaidConverterApp();
