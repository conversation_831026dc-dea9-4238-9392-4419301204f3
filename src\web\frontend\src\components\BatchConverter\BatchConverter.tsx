import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  CloudArrowUpIcon, 
  DocumentIcon, 
  XMarkIcon, 
  PlayIcon,
  PauseIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface BatchFile {
  id: string;
  file: File;
  content: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  outputUrl?: string;
  error?: string;
  size?: number;
}

interface BatchConverterProps {
  onError?: (error: string) => void;
  className?: string;
}

interface ConversionSettings {
  format: 'jpg' | 'png' | 'svg';
  quality: number;
  width: number;
  height: number;
  theme: 'default' | 'dark' | 'forest' | 'neutral';
  backgroundColor: string;
}

const BatchConverter: React.FC<BatchConverterProps> = ({
  onError,
  className = '',
}) => {
  const [files, setFiles] = useState<BatchFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [settings, setSettings] = useState<ConversionSettings>({
    format: 'png',
    quality: 90,
    width: 1920,
    height: 1080,
    theme: 'default',
    backgroundColor: '#ffffff',
  });

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newFiles: BatchFile[] = [];
    
    for (const file of acceptedFiles) {
      if (!file.name.toLowerCase().endsWith('.mmd')) {
        if (onError) {
          onError(`Invalid file type: ${file.name}. Only .mmd files are supported.`);
        }
        continue;
      }

      try {
        const content = await readFileContent(file);
        const batchFile: BatchFile = {
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          file,
          content,
          status: 'pending',
          progress: 0,
        };
        newFiles.push(batchFile);
      } catch (error) {
        if (onError) {
          onError(`Failed to read file: ${file.name}`);
        }
      }
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, [onError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.mmd'],
    },
    multiple: true,
  });

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  const clearAll = () => {
    setFiles([]);
  };

  const startBatchConversion = async () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    
    // Process files in parallel (max 3 at a time)
    const batchSize = 3;
    const pendingFiles = files.filter(f => f.status === 'pending' || f.status === 'error');
    
    for (let i = 0; i < pendingFiles.length; i += batchSize) {
      const batch = pendingFiles.slice(i, i + batchSize);
      const promises = batch.map(file => convertFile(file));
      await Promise.allSettled(promises);
    }
    
    setIsProcessing(false);
  };

  const convertFile = async (batchFile: BatchFile) => {
    try {
      // Update status to processing
      setFiles(prev => prev.map(f => 
        f.id === batchFile.id 
          ? { ...f, status: 'processing', progress: 10 }
          : f
      ));

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f => 
          f.id === batchFile.id && f.status === 'processing'
            ? { ...f, progress: Math.min(f.progress + 20, 90) }
            : f
        ));
      }, 500);

      // Make API call
      const response = await fetch('/api/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: batchFile.content,
          format: settings.format,
          options: {
            quality: settings.quality,
            width: settings.width,
            height: settings.height,
            theme: settings.theme,
            backgroundColor: settings.backgroundColor,
          },
        }),
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error(`Conversion failed: ${response.statusText}`);
      }

      // Create blob URL for download
      const blob = await response.blob();
      const outputUrl = URL.createObjectURL(blob);

      // Update status to completed
      setFiles(prev => prev.map(f => 
        f.id === batchFile.id 
          ? { 
              ...f, 
              status: 'completed', 
              progress: 100, 
              outputUrl,
              size: blob.size 
            }
          : f
      ));

    } catch (error) {
      // Update status to error
      setFiles(prev => prev.map(f => 
        f.id === batchFile.id 
          ? { 
              ...f, 
              status: 'error', 
              progress: 0,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          : f
      ));
    }
  };

  const downloadFile = (batchFile: BatchFile) => {
    if (!batchFile.outputUrl) return;
    
    const link = document.createElement('a');
    link.href = batchFile.outputUrl;
    link.download = `${batchFile.file.name.replace('.mmd', '')}.${settings.format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadAll = () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.outputUrl);
    completedFiles.forEach(file => downloadFile(file));
  };

  const getStatusIcon = (status: BatchFile['status']) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
      case 'processing':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-600" />;
    }
  };

  const getStatusColor = (status: BatchFile['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100';
      case 'processing':
        return 'bg-blue-50';
      case 'completed':
        return 'bg-green-50';
      case 'error':
        return 'bg-red-50';
    }
  };

  const completedCount = files.filter(f => f.status === 'completed').length;
  const errorCount = files.filter(f => f.status === 'error').length;
  const processingCount = files.filter(f => f.status === 'processing').length;

  return (
    <div className={`batch-converter ${className}`}>
      {/* Settings Panel */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Conversion Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Format</label>
            <select
              value={settings.format}
              onChange={(e) => setSettings(prev => ({ ...prev, format: e.target.value as any }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="png">PNG</option>
              <option value="jpg">JPEG</option>
              <option value="svg">SVG</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Quality</label>
            <input
              type="range"
              min="1"
              max="100"
              value={settings.quality}
              onChange={(e) => setSettings(prev => ({ ...prev, quality: parseInt(e.target.value) }))}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{settings.quality}%</span>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Theme</label>
            <select
              value={settings.theme}
              onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="default">Default</option>
              <option value="dark">Dark</option>
              <option value="forest">Forest</option>
              <option value="neutral">Neutral</option>
            </select>
          </div>
        </div>
      </div>

      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors mb-6
          ${isDragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
          }
        `}
      >
        <input {...getInputProps()} />
        <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
        <div className="mt-4">
          {isDragActive ? (
            <p className="text-blue-600 font-medium">Drop the .mmd files here...</p>
          ) : (
            <>
              <p className="text-gray-600 font-medium">
                Drag & drop multiple .mmd files here, or click to select
              </p>
              <p className="text-gray-500 text-sm mt-1">
                Supports batch processing of multiple files
              </p>
            </>
          )}
        </div>
      </div>

      {/* Files List */}
      {files.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">
              Files ({files.length})
            </h3>
            <div className="flex items-center space-x-2">
              {completedCount > 0 && (
                <button
                  onClick={downloadAll}
                  className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                  <span>Download All ({completedCount})</span>
                </button>
              )}
              <button
                onClick={startBatchConversion}
                disabled={isProcessing || files.every(f => f.status === 'completed')}
                className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm"
              >
                {isProcessing ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                <span>{isProcessing ? 'Processing...' : 'Start Conversion'}</span>
              </button>
              <button
                onClick={clearAll}
                className="text-gray-600 hover:text-red-600 text-sm"
              >
                Clear All
              </button>
            </div>
          </div>

          {/* Status Summary */}
          {(completedCount > 0 || errorCount > 0 || processingCount > 0) && (
            <div className="flex items-center space-x-4 mb-4 text-sm">
              {processingCount > 0 && (
                <span className="text-blue-600">Processing: {processingCount}</span>
              )}
              {completedCount > 0 && (
                <span className="text-green-600">Completed: {completedCount}</span>
              )}
              {errorCount > 0 && (
                <span className="text-red-600">Errors: {errorCount}</span>
              )}
            </div>
          )}

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {files.map((batchFile) => (
              <div
                key={batchFile.id}
                className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(batchFile.status)}`}
              >
                <div className="flex items-center space-x-3 flex-1">
                  {getStatusIcon(batchFile.status)}
                  <DocumentIcon className="h-6 w-6 text-blue-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {batchFile.file.name}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{(batchFile.file.size / 1024).toFixed(1)} KB</span>
                      {batchFile.status === 'processing' && (
                        <span>• {batchFile.progress}%</span>
                      )}
                      {batchFile.status === 'completed' && batchFile.size && (
                        <span>• Output: {(batchFile.size / 1024).toFixed(1)} KB</span>
                      )}
                      {batchFile.status === 'error' && batchFile.error && (
                        <span className="text-red-600">• {batchFile.error}</span>
                      )}
                    </div>
                    {batchFile.status === 'processing' && (
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div 
                          className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${batchFile.progress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {batchFile.status === 'completed' && batchFile.outputUrl && (
                    <button
                      onClick={() => downloadFile(batchFile)}
                      className="p-1 text-green-600 hover:text-green-700"
                      title="Download converted file"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    onClick={() => removeFile(batchFile.id)}
                    className="p-1 text-gray-400 hover:text-red-600"
                    title="Remove file"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BatchConverter;
