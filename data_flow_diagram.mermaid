graph TD
    %% Customer Input Processing
    CustomerMsg[💬 Customer Message] --> IntentClass[🎯 Intent Classification<br/>GPT-4 + Fine-tuned Model]
    IntentClass --> ConfidenceCheck{Confidence > 85%?}
    
    ConfidenceCheck -->|Yes| RouterDecision[🤖 Route to Specialist Agent]
    ConfidenceCheck -->|No| Clarification[❓ Request Clarification]
    
    %% Agent Selection Process
    RouterDecision --> AgentSelection{Agent Selection}
    AgentSelection --> ProductFlow[🛍️ Product Expert Path]
    AgentSelection --> OrderFlow[📦 Order Specialist Path]
    AgentSelection --> PolicyFlow[📋 Policy Advisor Path]
    AgentSelection --> TechFlow[🔧 Tech Support Path]

    %% Product Expert Flow
    ProductFlow --> ProductRAG[🔍 RAG Search<br/>Product Embeddings]
    ProductRAG --> ProductAPI[📡 Real-time API Call<br/>Inventory & Pricing]
    ProductAPI --> ProductResponse[📝 Generate Product Response]

    %% Order Specialist Flow
    OrderFlow --> OrderValidation[🔐 Customer Validation]
    OrderValidation --> OrderAPI[📡 Shopify Order API]
    OrderAPI --> OrderResponse[📝 Generate Order Response]

    %% Policy Advisor Flow
    PolicyFlow --> PolicyRAG[🔍 RAG Search<br/>Policy Documents]
    PolicyRAG --> PolicyContext[📄 Context Enrichment]
    PolicyContext --> PolicyResponse[📝 Generate Policy Response]

    %% Tech Support Flow
    TechFlow --> TechRAG[🔍 RAG Search<br/>Technical Docs]
    TechRAG --> DiagnosticTools[🛠️ Diagnostic Tools]
    DiagnosticTools --> TechResponse[📝 Generate Tech Response]

    %% Quality Assurance Pipeline
    ProductResponse --> QAPipeline[✅ Quality Assurance]
    OrderResponse --> QAPipeline
    PolicyResponse --> QAPipeline
    TechResponse --> QAPipeline

    QAPipeline --> FactCheck[🔍 Fact Verification]
    FactCheck --> SourceValidation[📋 Source Citation Check]
    SourceValidation --> ConfidenceScore[📊 Confidence Scoring]
    
    ConfidenceScore --> QADecision{QA Pass?}
    QADecision -->|Pass| FinalResponse[✨ Deliver Response]
    QADecision -->|Fail| HumanEscalation[👨‍💼 Escalate to Human]
    QADecision -->|Uncertain| ResponseFlag[⚠️ Flag & Deliver with Disclaimer]

    %% Data Sources (Right side)
    ProductRAG --> VectorStore[(🧠 Vector Store<br/>Product Embeddings)]
    PolicyRAG --> PolicyStore[(📚 Policy Vector Store)]
    TechRAG --> TechStore[(🔧 Technical Docs Store)]
    
    ProductAPI --> ShopifyDB[(🛒 Shopify Database)]
    OrderAPI --> ShopifyDB
    
    FactCheck --> FactDB[(✅ Fact Database<br/>Ground Truth)]

    %% Real-time Updates (Bottom)
    ShopifyDB --> WebhookProcessor[📡 Webhook Processor]
    WebhookProcessor --> CacheUpdate[⚡ Cache Invalidation]
    WebhookProcessor --> VectorUpdate[🔄 Vector Store Update]

    %% Styling
    classDef process fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef output fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class IntentClass,RouterDecision,ProductRAG,OrderValidation,PolicyRAG,TechRAG,QAPipeline,FactCheck process
    class ConfidenceCheck,AgentSelection,QADecision decision
    class VectorStore,PolicyStore,TechStore,ShopifyDB,FactDB data
    class FinalResponse,HumanEscalation,ResponseFlag output