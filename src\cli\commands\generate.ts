/**
 * Generate Command for ArchitekAI CLI
 * 
 * Handles the main diagram generation functionality from natural language descriptions.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { ConfigManager } from '@/core/config/manager';
import { NLPEngine } from '@/core/nlp/parser';
import { GeneratorFactory } from '@/core/generators/factory';
import { TemplateManager } from '@/core/templates/manager';
import { MermaidConverter } from '@/core/converter';
import { OutputFormat } from '@/types/architecture';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner, 
  displaySuccess, 
  displayError, 
  displayWarning,
  displayInfo,
  ensureOutputPath,
  formatDuration,
  formatFileSize,
  confirmAction,
} from '../utils';

const logger = createLogger('GenerateCommand');

interface GenerateOptions {
  output?: string;
  format?: OutputFormat;
  template?: string;
  interactive?: boolean;
  style?: string;
  validate?: boolean;
  preview?: boolean;
  overwrite?: boolean;
  verbose?: boolean;
}

export function generateCommand(configManager: ConfigManager): Command {
  const command = new Command('generate');

  command
    .description('Generate architecture diagrams from natural language descriptions')
    .argument('[description]', 'natural language description of the architecture')
    .option('-o, --output <path>', 'output file path')
    .option('-f, --format <format>', 'output format (mermaid, plantuml, ascii, drawio, lucidchart, jpg, png, svg)', 'mermaid')
    .option('-t, --template <name>', 'use a specific template as base')
    .option('-i, --interactive', 'enable interactive mode for refinement')
    .option('-s, --style <path>', 'path to custom style configuration')
    .option('--validate', 'validate the generated diagram')
    .option('--preview', 'preview the diagram before saving')
    .option('--overwrite', 'overwrite existing files without confirmation')
    .option('--verbose', 'enable verbose output')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architekAI generate "microservices with API gateway and user service"')}
  ${chalk.cyan('architekAI generate --interactive')}
  ${chalk.cyan('architekAI generate "e-commerce system" --format plantuml --output ./diagrams/ecommerce.puml')}
  ${chalk.cyan('architekAI generate "serverless architecture" --template serverless --validate')}
    `)
    .action(async (description: string | undefined, options: GenerateOptions, command: Command) => {
      // Get global options from parent command
      const globalOptions = command.parent?.opts() || {};
      const mergedOptions = { ...options, ...globalOptions };
      await executeGenerate(description, mergedOptions, configManager);
    });

  return command;
}

async function executeGenerate(
  description: string | undefined, 
  options: GenerateOptions, 
  configManager: ConfigManager
): Promise<void> {
  const startTime = Date.now();
  let spinner = createSpinner('Initializing...');
  
  try {
    spinner.start();

    // Get configuration
    const config = configManager.getConfig();
    
    // Merge options with config defaults
    // Handle --no-interactive flag properly
    let isInteractive = config.cli.interactive;
    if (options.interactive !== undefined) {
      isInteractive = options.interactive;
    }

    const mergedOptions = {
      format: (options.format || config.output.defaultFormat) as OutputFormat,
      interactive: isInteractive,
      validate: options.validate ?? true,
      verbose: options.verbose ?? config.cli.verbose,
      ...options,
    };

    spinner.text = 'Loading components...';

    // Initialize components
    const nlpEngine = new NLPEngine(config.nlp);
    const generatorFactory = new GeneratorFactory();
    const templateManager = new TemplateManager(config.templates);

    await templateManager.initialize();

    // Get description from user if not provided
    let finalDescription = description;
    if (!finalDescription) {
      spinner.stop();
      
      if (mergedOptions.interactive) {
        finalDescription = await promptForDescription();
      } else {
        displayError('Description is required. Use --interactive flag for guided input.');
        process.exit(1);
      }
      
      spinner = createSpinner('Processing description...');
      spinner.start();
    }

    if (!finalDescription?.trim()) {
      throw new Error('Description cannot be empty');
    }

    displayInfo(`Processing: "${finalDescription}"`);
    
    // Parse the description
    spinner.text = 'Analyzing description with NLP...';
    const parseResult = await nlpEngine.parse(finalDescription);
    
    if (parseResult.confidence < config.nlp.confidence.minimum) {
      spinner.stop();
      displayWarning(`Low confidence in parsing (${Math.round(parseResult.confidence * 100)}%). Consider refining your description.`);
      
      if (mergedOptions.interactive) {
        const shouldContinue = await confirmAction('Continue anyway?', false);
        if (!shouldContinue) {
          displayInfo('Operation cancelled.');
          return;
        }
      }
      
      spinner = createSpinner('Continuing with generation...');
      spinner.start();
    }

    // Apply template if specified
    let architecture = parseResult.architecture;
    if (mergedOptions.template) {
      spinner.text = `Applying template: ${mergedOptions.template}`;
      const template = await templateManager.getTemplate(mergedOptions.template);
      architecture = await templateManager.applyTemplate(template, architecture);
    }

    // Interactive refinement
    if (mergedOptions.interactive) {
      spinner.stop();
      architecture = await interactiveRefinement(architecture, parseResult);
      spinner = createSpinner('Generating diagram...');
      spinner.start();
    }

    // Handle image formats by first generating Mermaid, then converting
    const isImageFormat = ['jpg', 'jpeg', 'png', 'svg'].includes(mergedOptions.format);
    const generationFormat = isImageFormat ? OutputFormat.MERMAID : (mergedOptions.format as OutputFormat);

    // Generate the diagram
    spinner.text = `Generating ${generationFormat} diagram...`;
    const generator = generatorFactory.createGenerator(generationFormat);
    const generatorOptions = {
      style: mergedOptions.style ? await loadStyleConfig(mergedOptions.style) : undefined,
      output: {
        filename: mergedOptions.output || 'architecture',
        format: generationFormat,
      },
      features: {
        includeMetadata: true,
        includeComments: mergedOptions.verbose,
        includeValidation: mergedOptions.validate,
      },
    };

    const result = await generator.generate(architecture, generatorOptions);

    // Validation
    if (mergedOptions.validate) {
      spinner.text = 'Validating diagram...';
      const validation = await generator.validate(architecture);
      
      if (!validation.isValid) {
        spinner.stop();
        displayWarning('Validation issues found:');
        validation.errors.forEach(error => {
          console.log(chalk.red(`  • ${error.message}`));
        });
        
        if (validation.warnings.length > 0) {
          console.log(chalk.yellow('\nWarnings:'));
          validation.warnings.forEach(warning => {
            console.log(chalk.yellow(`  • ${warning.message}`));
          });
        }
        
        const shouldContinue = await confirmAction('Continue despite validation issues?', true);
        if (!shouldContinue) {
          displayInfo('Operation cancelled.');
          return;
        }
        
        spinner = createSpinner('Finalizing...');
        spinner.start();
      }
    }

    // Preview
    if (mergedOptions.preview) {
      spinner.stop();
      await previewDiagram(result.content, mergedOptions.format);
      
      const shouldSave = await confirmAction('Save the diagram?', true);
      if (!shouldSave) {
        displayInfo('Diagram not saved.');
        return;
      }
      
      spinner = createSpinner('Saving diagram...');
      spinner.start();
    }

    // Handle image format conversion
    let finalOutputPath: string;

    if (isImageFormat) {
      // For image formats, we need to convert from Mermaid
      spinner.text = `Converting to ${mergedOptions.format.toUpperCase()}...`;

      // First save the Mermaid content to a temporary file
      const tempMermaidPath = await saveTempMermaidFile(result.content, architecture.name);

      try {
        // Initialize converter
        const converter = new MermaidConverter();
        await converter.initialize();

        // Convert to image format with high quality settings
        const conversionResult = await converter.convertFile(tempMermaidPath, {
          format: mergedOptions.format as 'jpg' | 'jpeg' | 'png' | 'svg',
          quality: 95,
          width: 1920,
          height: 1080,
          scale: 2, // 2x scale for high quality
          theme: 'default',
          backgroundColor: '#ffffff',
          overwrite: true,
          qualityPreset: 'web', // Use web quality preset
        });

        if (!conversionResult.success) {
          throw new Error(conversionResult.error || 'Conversion failed');
        }

        finalOutputPath = conversionResult.outputFile;

        // Clean up converter
        await converter.cleanup();

        // Clean up temp file
        await cleanupTempFile(tempMermaidPath);

      } catch (error) {
        await cleanupTempFile(tempMermaidPath);
        throw error;
      }
    } else {
      // Save the result normally for non-image formats
      finalOutputPath = await determineOutputPath(
        mergedOptions.output,
        architecture.name,
        mergedOptions.format,
        config.output.directory
      );

      // Check for existing file
      if (!mergedOptions.overwrite && await fileExists(finalOutputPath)) {
        spinner.stop();
        const shouldOverwrite = await confirmAction(`File ${finalOutputPath} already exists. Overwrite?`, false);
        if (!shouldOverwrite) {
          displayInfo('Operation cancelled.');
          return;
        }
        spinner = createSpinner('Saving diagram...');
        spinner.start();
      }

      spinner.text = 'Saving diagram...';
      await saveResult(result.content, finalOutputPath);
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    spinner.succeed(chalk.green('Diagram generated successfully!'));

    // Display summary
    displaySuccess(`Generated ${mergedOptions.format} diagram`);
    displayInfo(`Output: ${finalOutputPath}`);
    displayInfo(`Size: ${formatFileSize(Buffer.byteLength(result.content, 'utf8'))}`);
    displayInfo(`Duration: ${formatDuration(duration)}`);
    displayInfo(`Components: ${architecture.components.length}`);
    displayInfo(`Connections: ${architecture.connections.length}`);

    if (result.warnings.length > 0) {
      console.log(chalk.yellow('\nWarnings:'));
      result.warnings.forEach(warning => {
        console.log(chalk.yellow(`  • ${warning.message}`));
      });
    }

    if (mergedOptions.verbose) {
      console.log(chalk.gray('\nGeneration Statistics:'));
      console.log(chalk.gray(`  Processing Time: ${formatDuration(result.statistics.processingTime)}`));
      console.log(chalk.gray(`  Complexity Score: ${result.statistics.complexity.structural}`));
      console.log(chalk.gray(`  Confidence: ${Math.round(parseResult.confidence * 100)}%`));
    }

  } catch (error) {
    if (spinner) {
      spinner.fail(chalk.red('Generation failed'));
    }
    
    logger.error('Generate command failed:', error);
    
    if (error instanceof Error) {
      displayError(error.message);
    } else {
      displayError('An unexpected error occurred during generation');
    }
    
    process.exit(1);
  }
}

async function promptForDescription(): Promise<string> {
  const questions = [
    {
      type: 'input',
      name: 'description',
      message: 'Describe your architecture:',
      validate: (input: string) => {
        if (!input.trim()) {
          return 'Description cannot be empty';
        }
        if (input.length < 10) {
          return 'Please provide a more detailed description (at least 10 characters)';
        }
        return true;
      },
    },
  ];

  const answers = await inquirer.prompt(questions);
  return answers.description as string;
}

async function interactiveRefinement(architecture: any, parseResult: any): Promise<any> {
  displayInfo('Interactive refinement mode');
  
  // Show current understanding
  console.log(chalk.cyan('\nCurrent understanding:'));
  console.log(`Components: ${architecture.components.map((c: any) => c.name).join(', ')}`);
  console.log(`Pattern: ${parseResult.suggestedPattern}`);
  console.log(`Confidence: ${Math.round(parseResult.confidence * 100)}%`);

  const questions = [
    {
      type: 'confirm',
      name: 'addComponents',
      message: 'Would you like to add more components?',
      default: false,
    },
    {
      type: 'confirm',
      name: 'modifyConnections',
      message: 'Would you like to modify connections?',
      default: false,
    },
    {
      type: 'confirm',
      name: 'changePattern',
      message: 'Would you like to change the architecture pattern?',
      default: false,
    },
  ];

  const refinements = await inquirer.prompt(questions);
  
  // Apply refinements (simplified for now)
  if (refinements.addComponents) {
    displayInfo('Component addition feature coming soon!');
  }
  
  if (refinements.modifyConnections) {
    displayInfo('Connection modification feature coming soon!');
  }
  
  if (refinements.changePattern) {
    displayInfo('Pattern change feature coming soon!');
  }

  return architecture;
}

async function saveTempMermaidFile(content: string, name: string): Promise<string> {
  const fs = await import('fs-extra');
  const os = await import('os');
  const path = await import('path');

  const tempDir = os.tmpdir();
  const tempFileName = `architek-ai-${name}-${Date.now()}.mmd`;
  const tempPath = path.join(tempDir, tempFileName);

  await fs.writeFile(tempPath, content, 'utf8');
  return tempPath;
}

async function cleanupTempFile(filePath: string): Promise<void> {
  try {
    const fs = await import('fs-extra');
    if (await fs.pathExists(filePath)) {
      await fs.remove(filePath);
    }
  } catch (error) {
    // Ignore cleanup errors
    logger.warn(`Failed to cleanup temp file ${filePath}:`, error);
  }
}

async function loadStyleConfig(stylePath: string): Promise<any> {
  // Implementation for loading custom style configuration
  displayInfo(`Loading style configuration from: ${stylePath}`);
  return {};
}

async function previewDiagram(content: string, format: OutputFormat): Promise<void> {
  console.log(chalk.cyan('\n--- Diagram Preview ---'));
  
  if (format === OutputFormat.ASCII) {
    console.log(content);
  } else {
    // For other formats, show a truncated version
    const lines = content.split('\n');
    const preview = lines.slice(0, 20).join('\n');
    console.log(preview);
    
    if (lines.length > 20) {
      console.log(chalk.gray(`... (${lines.length - 20} more lines)`));
    }
  }
  
  console.log(chalk.cyan('--- End Preview ---\n'));
}

async function determineOutputPath(
  specifiedPath: string | undefined,
  architectureName: string,
  format: OutputFormat,
  defaultDirectory: string
): Promise<string> {
  if (specifiedPath) {
    return await ensureOutputPath(specifiedPath);
  }

  const extension = getFileExtension(format);
  const filename = `${architectureName.toLowerCase().replace(/\s+/g, '-')}.${extension}`;
  const fullPath = require('path').join(defaultDirectory, filename);
  
  return await ensureOutputPath(fullPath);
}

function getFileExtension(format: OutputFormat): string {
  const extensions: Record<OutputFormat, string> = {
    [OutputFormat.MERMAID]: 'mmd',
    [OutputFormat.PLANTUML]: 'puml',
    [OutputFormat.ASCII]: 'txt',
    [OutputFormat.DRAWIO]: 'drawio',
    [OutputFormat.LUCIDCHART]: 'json',
    [OutputFormat.JSON]: 'json',
    [OutputFormat.YAML]: 'yaml',
    [OutputFormat.SVG]: 'svg',
    [OutputFormat.PNG]: 'png',
    [OutputFormat.PDF]: 'pdf',
  };
  
  return extensions[format] || 'txt';
}

async function fileExists(filePath: string): Promise<boolean> {
  try {
    const fs = await import('fs-extra');
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function saveResult(content: string, outputPath: string): Promise<void> {
  const fs = await import('fs-extra');
  await fs.writeFile(outputPath, content, 'utf8');
}
