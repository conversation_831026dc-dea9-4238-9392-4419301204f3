sequenceDiagram
    participant C as 👤 Customer
    participant R as 🧠 Router Agent
    participant P as 🛍️ Product Agent
    participant O as 📦 Order Agent
    participant Q as ✅ QA Agent
    participant DB as 🗄️ Database
    participant API as 🛒 Shopify API

    C->>R: "Can I return this sweater I ordered last week?"
    
    Note over R: Intent Classification<br/>Mixed: Return Policy + Order Lookup
    
    R->>R: Analyze: Requires Order Info + Policy
    R->>O: Request: Find recent sweater orders
    R->>P: Request: Get return policy for sweaters
    
    par Parallel Agent Processing
        O->>DB: Query recent orders for customer
        DB-->>O: Order #12345: Sweater, ordered 5 days ago
        O-->>R: Found: Order #12345, eligible timeframe
    and
        P->>DB: Search return policies for sweaters
        DB-->>P: 30-day return policy, original condition
        P-->>R: Policy: 30 days, must be unworn
    end
    
    R->>R: Combine: Order details + Policy requirements
    R->>Q: Verify: Order exists + Policy accurate?
    
    Q->>API: Validate order status
    API-->>Q: Confirmed: Order #12345, shipped 5 days ago
    Q->>DB: Cross-check return policy
    DB-->>Q: Confirmed: 30-day return policy
    
    Q-->>R: ✅ Verified: All information accurate
    
    R->>C: "Yes! Your sweater from Order #12345 can be returned within 30 days. Since you ordered it 5 days ago, you have 25 days left. Please ensure it's unworn with tags attached."

    Note over R,C: Alternative: Complex Query Requiring Escalation
    
    C->>R: "I want to modify my bulk order and change shipping address"
    R->>R: High complexity + Multiple systems involved
    R->>Q: Risk assessment: Bulk order modification
    Q-->>R: ⚠️ Recommend human agent for bulk changes
    R->>C: "For bulk order modifications, I'll connect you with a specialist who can safely handle these changes."