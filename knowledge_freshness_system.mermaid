graph TB
    %% External Data Sources
    ShopifyStore[🛒 Shopify Store] --> WebhookEndpoint[📡 Webhook Endpoint]
    Website[🌐 Store Website] --> WebCrawler[🕷️ Web Crawler]
    CMS[📝 Content Management] --> ContentAPI[📋 Content API]
    
    %% Event Processing
    WebhookEndpoint --> EventRouter{📨 Event Router}
    EventRouter --> ProductEvents[🛍️ Product Updates]
    EventRouter --> OrderEvents[📦 Order Updates]
    EventRouter --> InventoryEvents[📊 Inventory Changes]
    EventRouter --> PriceEvents[💰 Price Updates]

    %% Real-time Processing (Hot Path)
    ProductEvents --> HotCache[⚡ Hot Cache<br/>Redis]
    OrderEvents --> HotCache
    InventoryEvents --> HotCache
    PriceEvents --> HotCache
    
    HotCache --> CacheInvalidation[🗑️ Cache Invalidation]
    CacheInvalidation --> ActiveSessions[💬 Notify Active Chats]

    %% Scheduled Processing (Warm Path)
    WebCrawler --> ScheduledProcessor[⏰ Scheduled Processor]
    ContentAPI --> ScheduledProcessor
    
    ScheduledProcessor --> ContentClassifier{📂 Content Type}
    ContentClassifier --> PolicyUpdates[📋 Policy Changes]
    ContentClassifier --> ProductDescriptions[📝 Product Info]
    ContentClassifier --> FAQUpdates[❓ FAQ Changes]
    ContentClassifier --> BlogContent[📰 Blog Posts]

    %% Vector Database Updates
    PolicyUpdates --> VectorProcessor[🧠 Vector Processor]
    ProductDescriptions --> VectorProcessor
    FAQUpdates --> VectorProcessor
    BlogContent --> VectorProcessor
    
    VectorProcessor --> EmbeddingGeneration[🔢 Generate Embeddings<br/>OpenAI API]
    EmbeddingGeneration --> VectorDB[(🧠 Vector Database<br/>Pinecone/Weaviate)]

    %% Database Updates
    ProductEvents --> DBProcessor[🗄️ Database Processor]
    OrderEvents --> DBProcessor
    InventoryEvents --> DBProcessor
    PriceEvents --> DBProcessor
    
    DBProcessor --> PostgreSQL[(🗄️ PostgreSQL<br/>Primary Database)]

    %% Version Control & Quality Gates
    VectorProcessor --> VersionControl[📋 Version Control]
    DBProcessor --> VersionControl
    
    VersionControl --> QualityGates{✅ Quality Gates}
    QualityGates -->|Pass| ProductionDeploy[🚀 Production Deploy]
    QualityGates -->|Fail| RollbackQueue[↩️ Rollback Queue]
    QualityGates -->|Review| ManualReview[👨‍💼 Manual Review]

    %% Monitoring & Health Checks
    ProductionDeploy --> HealthMonitor[❤️ Health Monitor]
    HealthMonitor --> DataFreshnessCheck[🕐 Freshness Check]
    HealthMonitor --> ConsistencyValidation[🔍 Consistency Validation]
    HealthMonitor --> PerformanceMetrics[📊 Performance Metrics]

    %% Update Frequency Legend
    subgraph "🔥 Real-time (< 1 minute)"
        direction LR
        RT1[Inventory Levels]
        RT2[Order Status]
        RT3[Flash Sales]
    end

    subgraph "⏰ Scheduled Updates"
        direction LR
        S1[Every 15min: Product prices]
        S2[Hourly: New products]
        S3[Daily: Policies & FAQs]
    end

    %% Alert System
    DataFreshnessCheck --> AlertSystem[🚨 Alert System]
    ConsistencyValidation --> AlertSystem
    AlertSystem --> SlackNotification[📱 Slack Alerts]
    AlertSystem --> EmailAlerts[📧 Email Alerts]
    AlertSystem --> PagerDuty[📟 PagerDuty]

    %% Styling
    classDef realtime fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef scheduled fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef storage fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processing fill:#e3f2fd,stroke:#1565c0,stroke-width:2px

    class WebhookEndpoint,HotCache,CacheInvalidation,ActiveSessions realtime
    class ScheduledProcessor,VectorProcessor,ContentClassifier scheduled
    class VectorDB,PostgreSQL,HotCache storage
    class EventRouter,DBProcessor,EmbeddingGeneration processing