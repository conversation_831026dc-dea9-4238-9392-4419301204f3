import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import sharp from 'sharp';
import * as fs from 'fs-extra';
import * as path from 'path';

// Quality presets for different use cases
export const QUALITY_PRESETS = {
  'web': {
    scale: 2,
    quality: 90,
    width: 1920,
    height: 1080,
    sharpOptions: {
      progressive: true,
      optimizeScans: true
    }
  },
  'print': {
    scale: 2,
    quality: 95,
    width: 3840,
    height: 2160,
    sharpOptions: {
      progressive: false,
      optimizeScans: false,
      mozjpeg: true
    }
  },
  'presentation': {
    scale: 1.5,
    quality: 90,
    width: 2560,
    height: 1440,
    sharpOptions: {
      progressive: true,
      optimizeScans: true
    }
  },
  'poster': {
    scale: 3,
    quality: 98,
    width: 5760,
    height: 3240,
    sharpOptions: {
      progressive: false,
      optimizeScans: false,
      mozjpeg: true
    }
  },
  'ultra-high': {
    scale: 4,
    quality: 100,
    width: 7680,
    height: 4320,
    sharpOptions: {
      progressive: false,
      optimizeScans: false,
      mozjpeg: false,
      nearLossless: true as any
    }
  },
  'print-ready': {
    scale: 5,
    quality: 100,
    width: 9600,
    height: 5400,
    sharpOptions: {
      progressive: false,
      optimizeScans: false,
      mozjpeg: false,
      nearLossless: true as any,
      effort: 6 as any
    }
  }
} as const;

export interface ConversionOptions {
  format: 'jpg' | 'jpeg' | 'png' | 'svg' | 'pdf';
  quality?: number;
  width?: number;
  height?: number;
  scale?: number;
  backgroundColor?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  outputDir?: string;
  outputFileName?: string;
  overwrite?: boolean;
  qualityPreset?: 'web' | 'print' | 'presentation' | 'poster' | 'ultra-high' | 'print-ready';
}

export interface ConversionResult {
  inputFile: string;
  outputFile: string;
  format: string;
  size: number;
  width: number;
  height: number;
  success: boolean;
  error?: string;
  duration: number;
}

export class MermaidConverter {
  private browser: Browser | null = null;
  private isInitialized = false;

  constructor() {}

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--no-first-run',
          '--no-zygote',
          // High-quality rendering flags
          '--enable-gpu-rasterization',
          '--enable-accelerated-2d-canvas',

          '--high-dpi-support=1',
          '--force-color-profile=srgb',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding'
        ]
      });

      this.isInitialized = true;
    } catch (error) {
      throw new Error(`Converter initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
    }
  }

  async convertFile(inputFile: string, options: ConversionOptions): Promise<ConversionResult> {
    const startTime = Date.now();

    if (!this.isInitialized || !this.browser) {
      throw new Error('Converter not initialized');
    }

    try {
      // Apply quality preset if specified
      let effectiveOptions = { ...options };
      if (options.qualityPreset && QUALITY_PRESETS[options.qualityPreset]) {
        const preset = QUALITY_PRESETS[options.qualityPreset];
        effectiveOptions = {
          ...effectiveOptions,
          width: effectiveOptions.width || preset.width,
          height: effectiveOptions.height || preset.height,
          scale: effectiveOptions.scale || preset.scale,
          quality: effectiveOptions.quality || preset.quality
        };
      }

      // Read input file
      const mermaidContent = await fs.readFile(inputFile, 'utf8');

      // Generate output path
      const outputPath = this.generateOutputPath(inputFile, effectiveOptions);
      await fs.ensureDir(path.dirname(outputPath));

      // Create page
      const page = await this.browser.newPage();
      
      try {
        // Set viewport with proper scale factor calculation
        const width = effectiveOptions.width || 1920;
        const height = effectiveOptions.height || 1080;
        const scale = effectiveOptions.scale || 2; // Default to 2x for better quality

        await page.setViewport({
          width: Math.floor(width / scale),
          height: Math.floor(height / scale),
          deviceScaleFactor: scale,
        });

        // Create HTML content with Mermaid
        const htmlContent = this.createMermaidHTML(mermaidContent, effectiveOptions);
        await page.setContent(htmlContent);

        // Wait for Mermaid to render
        await page.waitForSelector('#mermaid-diagram svg', { timeout: 30000 });

        let outputBuffer: Buffer;
        let dimensions = { width: 0, height: 0 };

        if (options.format === 'svg') {
          // Get SVG content
          const svgContent = await page.$eval('#mermaid-diagram svg', (svg) => svg.outerHTML);
          outputBuffer = Buffer.from(svgContent, 'utf8');
          
          // Extract dimensions from SVG
          const svgElement = await page.$('#mermaid-diagram svg');
          if (svgElement) {
            const box = await svgElement.boundingBox();
            if (box) {
              dimensions = { width: Math.round(box.width), height: Math.round(box.height) };
            }
          }
        } else {
          // Take screenshot
          const element = await page.$('#mermaid-diagram');
          if (!element) {
            throw new Error('Mermaid diagram not found');
          }

          const screenshotBuffer = await element.screenshot({
            type: effectiveOptions.format === 'jpg' || effectiveOptions.format === 'jpeg' ? 'jpeg' : 'png',
            quality: effectiveOptions.format === 'jpg' || effectiveOptions.format === 'jpeg' ? effectiveOptions.quality || 90 : undefined,
          });

          // Process with Sharp for enhanced quality
          const sharpImage = sharp(screenshotBuffer);
          const metadata = await sharpImage.metadata();
          dimensions = { width: metadata.width || 0, height: metadata.height || 0 };

          // Apply quality preset Sharp options if available
          const presetSharpOptions = effectiveOptions.qualityPreset && QUALITY_PRESETS[effectiveOptions.qualityPreset]
            ? QUALITY_PRESETS[effectiveOptions.qualityPreset].sharpOptions
            : {};

          if (effectiveOptions.format === 'jpg' || effectiveOptions.format === 'jpeg') {
            // Enhanced JPEG processing with preset options
            const jpegOptions: any = {
              quality: effectiveOptions.quality || 95,
              progressive: (presetSharpOptions as any).progressive !== undefined ? (presetSharpOptions as any).progressive : true,
              optimizeScans: (presetSharpOptions as any).optimizeScans !== undefined ? (presetSharpOptions as any).optimizeScans : true,
              mozjpeg: (presetSharpOptions as any).mozjpeg !== undefined ? (presetSharpOptions as any).mozjpeg : true
            };
            outputBuffer = await sharpImage.jpeg(jpegOptions).toBuffer();
          } else {
            // Enhanced PNG processing with preset options
            const pngOptions: any = {
              quality: 100,
              compressionLevel: 9,
              adaptiveFiltering: (presetSharpOptions as any).adaptiveFiltering !== undefined ? (presetSharpOptions as any).adaptiveFiltering : true,
              palette: false
            };

            // Add optional properties if they exist in preset
            if ((presetSharpOptions as any).nearLossless !== undefined) {
              pngOptions.nearLossless = (presetSharpOptions as any).nearLossless;
            }
            if ((presetSharpOptions as any).effort !== undefined) {
              pngOptions.effort = (presetSharpOptions as any).effort;
            }

            outputBuffer = await sharpImage.png(pngOptions).toBuffer();
          }
        }

        // Write output file
        await fs.writeFile(outputPath, outputBuffer);

        const duration = Date.now() - startTime;
        const stats = await fs.stat(outputPath);

        return {
          inputFile,
          outputFile: outputPath,
          format: effectiveOptions.format,
          size: stats.size,
          width: dimensions.width,
          height: dimensions.height,
          success: true,
          duration,
        };

      } finally {
        await page.close();
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        inputFile,
        outputFile: '',
        format: options.format,
        size: 0,
        width: 0,
        height: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
      };
    }
  }

  private generateOutputPath(inputFile: string, options: ConversionOptions): string {
    const inputDir = path.dirname(inputFile);
    const inputName = path.basename(inputFile, '.mmd');
    const outputName = options.outputFileName || inputName;
    const outputDir = options.outputDir || path.join(inputDir, 'converted-images');
    
    return path.join(outputDir, `${outputName}.${options.format}`);
  }

  private createMermaidHTML(mermaidContent: string, options: ConversionOptions): string {
    const theme = options.theme || 'default';
    const backgroundColor = options.backgroundColor || '#ffffff';

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mermaid Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: ${backgroundColor};
            font-family: Arial, sans-serif;
        }
        #mermaid-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .mermaid {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div id="mermaid-diagram">
        <div class="mermaid">
${mermaidContent}
        </div>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: '${theme}',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif',
            fontSize: 16,
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                wrap: true
            },
            gantt: {
                useMaxWidth: true
            }
        });
    </script>
</body>
</html>`;
  }
}
