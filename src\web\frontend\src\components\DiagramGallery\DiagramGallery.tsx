import React, { useState, useEffect } from 'react';
import { EyeIcon, ArrowDownTrayIcon, TrashIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';
import { MermaidPreview } from '../MermaidPreview';

interface DiagramItem {
  id: string;
  name: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  description?: string;
  tags?: string[];
  thumbnail?: string;
}

interface DiagramGalleryProps {
  onSelectDiagram?: (diagram: DiagramItem) => void;
  onDeleteDiagram?: (id: string) => void;
  onDuplicateDiagram?: (diagram: DiagramItem) => void;
  className?: string;
}

const DiagramGallery: React.FC<DiagramGalleryProps> = ({
  onSelectDiagram,
  onDeleteDiagram,
  onDuplicateDiagram,
  className = '',
}) => {
  const [diagrams, setDiagrams] = useState<DiagramItem[]>([]);
  const [_selectedDiagram, _setSelectedDiagram] = useState<DiagramItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'updatedAt'>('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadDiagrams();
  }, []);

  const loadDiagrams = async () => {
    setIsLoading(true);
    try {
      // Load diagrams from localStorage for now
      // In a real app, this would be an API call
      const savedDiagrams = localStorage.getItem('architek-ai-diagrams');
      if (savedDiagrams) {
        const parsed = JSON.parse(savedDiagrams);
        setDiagrams(parsed.map((d: any) => ({
          ...d,
          createdAt: new Date(d.createdAt),
          updatedAt: new Date(d.updatedAt),
        })));
      }
    } catch (error) {
      console.error('Failed to load diagrams:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveDiagrams = (updatedDiagrams: DiagramItem[]) => {
    localStorage.setItem('architek-ai-diagrams', JSON.stringify(updatedDiagrams));
    setDiagrams(updatedDiagrams);
  };

  const handleDeleteDiagram = (id: string) => {
    if (window.confirm('Are you sure you want to delete this diagram?')) {
      const updatedDiagrams = diagrams.filter(d => d.id !== id);
      saveDiagrams(updatedDiagrams);
      
      if (onDeleteDiagram) {
        onDeleteDiagram(id);
      }
    }
  };

  const handleDuplicateDiagram = (diagram: DiagramItem) => {
    const duplicated: DiagramItem = {
      ...diagram,
      id: `${diagram.id}-copy-${Date.now()}`,
      name: `${diagram.name} (Copy)`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const updatedDiagrams = [duplicated, ...diagrams];
    saveDiagrams(updatedDiagrams);
    
    if (onDuplicateDiagram) {
      onDuplicateDiagram(duplicated);
    }
  };

  const handleExportDiagram = async (diagram: DiagramItem, format: 'jpg' | 'png' | 'svg') => {
    try {
      const response = await fetch('/api/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: diagram.content,
          format,
          options: {
            quality: 90,
            width: 1920,
            height: 1080,
            theme: 'default',
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${diagram.name}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
      alert(error instanceof Error ? error.message : 'Export failed');
    }
  };

  const filteredAndSortedDiagrams = diagrams
    .filter(diagram => 
      diagram.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      diagram.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      diagram.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (aValue instanceof Date && bValue instanceof Date) {
        return sortOrder === 'asc' 
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }
      
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      
      return sortOrder === 'asc' 
        ? aStr.localeCompare(bStr)
        : bStr.localeCompare(aStr);
    });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading diagrams...</span>
      </div>
    );
  }

  return (
    <div className={`diagram-gallery ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h2 className="text-2xl font-bold text-gray-900">Diagram Gallery</h2>
          
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search diagrams..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-3 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            {/* Sort */}
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as 'name' | 'createdAt' | 'updatedAt');
                setSortOrder(order as 'asc' | 'desc');
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="updatedAt-desc">Recently Updated</option>
              <option value="createdAt-desc">Recently Created</option>
              <option value="name-asc">Name A-Z</option>
              <option value="name-desc">Name Z-A</option>
            </select>
          </div>
        </div>
      </div>

      {/* Gallery Grid */}
      {filteredAndSortedDiagrams.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📊</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No diagrams found</h3>
          <p className="text-gray-600">
            {searchTerm ? 'Try adjusting your search terms.' : 'Create your first diagram to get started.'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedDiagrams.map((diagram) => (
            <div
              key={diagram.id}
              className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
            >
              {/* Thumbnail */}
              <div className="aspect-video bg-gray-50 rounded-t-lg overflow-hidden">
                <MermaidPreview
                  content={diagram.content}
                  className="w-full h-full"
                  theme="default"
                />
              </div>
              
              {/* Content */}
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-1 truncate">
                  {diagram.name}
                </h3>
                
                {diagram.description && (
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {diagram.description}
                  </p>
                )}
                
                <div className="text-xs text-gray-500 mb-3">
                  Updated {diagram.updatedAt.toLocaleDateString()}
                </div>
                
                {/* Tags */}
                {diagram.tags && diagram.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {diagram.tags.slice(0, 3).map((tag, index) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                      >
                        {tag}
                      </span>
                    ))}
                    {diagram.tags.length > 3 && (
                      <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                        +{diagram.tags.length - 3}
                      </span>
                    )}
                  </div>
                )}
                
                {/* Actions */}
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => {
                      _setSelectedDiagram(diagram);
                      if (onSelectDiagram) {
                        onSelectDiagram(diagram);
                      }
                    }}
                    className="inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
                  >
                    <EyeIcon className="h-4 w-4 mr-1" />
                    View
                  </button>
                  
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => handleExportDiagram(diagram, 'jpg')}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="Export as JPG"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => handleDuplicateDiagram(diagram)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                      title="Duplicate"
                    >
                      <DocumentDuplicateIcon className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => handleDeleteDiagram(diagram.id)}
                      className="p-1 text-gray-400 hover:text-red-600"
                      title="Delete"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DiagramGallery;
