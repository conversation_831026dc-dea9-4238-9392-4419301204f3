<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Converter</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .drag-over {
            border-color: #3b82f6 !important;
            background-color: #eff6ff !important;
        }
        
        .progress-bar {
            transition: width 0.3s ease;
        }
        
        .file-item {
            transition: all 0.2s ease;
        }
        
        .file-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .status-pending { color: #6b7280; }
        .status-processing { color: #3b82f6; }
        .status-completed { color: #10b981; }
        .status-error { color: #ef4444; }
        
        .spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div id="app" class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Mermaid Converter</h1>
                        <p class="text-gray-600">Convert Mermaid diagrams to high-quality images</p>
                    </div>
                    <div class="text-right">
                        <div id="app-info" class="text-sm text-gray-500"></div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="container mx-auto px-6 py-8">
            <!-- Tab Navigation -->
            <div class="bg-white rounded-lg shadow-md mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex">
                        <button id="tab-generate" class="tab-button py-2 px-4 border-b-2 border-blue-500 text-blue-600 font-medium">
                            AI Generate
                        </button>
                        <button id="tab-convert" class="tab-button py-2 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium">
                            Convert Files
                        </button>
                    </nav>
                </div>
            </div>

            <!-- AI Generation Tab -->
            <div id="generate-tab" class="tab-content">
                <!-- AI Generation Panel -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-lg font-semibold mb-4">AI Diagram Generation</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="ai-description"
                                      placeholder="Describe your architecture (e.g., microservices architecture with API gateway, user service, and payment service)"
                                      class="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Output Format</label>
                                <select id="ai-output-format" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="mermaid">Mermaid Code</option>
                                    <option value="png">PNG Image</option>
                                    <option value="jpg">JPEG Image</option>
                                    <option value="svg">SVG Vector</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Template</label>
                                <select id="ai-template" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="">No Template</option>
                                    <option value="microservices">Microservices</option>
                                    <option value="monolithic">Monolithic</option>
                                    <option value="serverless">Serverless</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                                <select id="ai-theme" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="default">Default</option>
                                    <option value="dark">Dark</option>
                                    <option value="forest">Forest</option>
                                    <option value="neutral">Neutral</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex space-x-4">
                            <button id="ai-generate-btn"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-semibold disabled:bg-gray-400">
                                Generate Diagram
                            </button>
                            <button id="ai-save-btn"
                                    class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-semibold hidden">
                                Save Result
                            </button>
                        </div>
                    </div>
                </div>

                <!-- AI Generation Results -->
                <div id="ai-results-section" class="bg-white rounded-lg shadow-md p-6 hidden">
                    <h2 class="text-lg font-semibold mb-4">Generated Diagram</h2>
                    <div id="ai-results-content"></div>
                </div>
            </div>

            <!-- Convert Files Tab -->
            <div id="convert-tab" class="tab-content hidden">
            <!-- Settings Panel -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-lg font-semibold mb-4">Conversion Settings</h2>
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Format</label>
                        <select id="format" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="png">PNG</option>
                            <option value="jpg">JPEG</option>
                            <option value="svg">SVG</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Quality Preset</label>
                        <select id="quality-preset" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="">Custom Settings</option>
                            <option value="web">Web (1920x1080)</option>
                            <option value="print">Print (3840x2160)</option>
                            <option value="presentation">Presentation (2560x1440)</option>
                            <option value="poster">Poster (5760x3240)</option>
                            <option value="ultra-high">Ultra High (7680x4320)</option>
                            <option value="print-ready">Print Ready (9600x5400)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Quality</label>
                        <input type="range" id="quality" min="1" max="100" value="90" class="w-full">
                        <span id="quality-value" class="text-sm text-gray-500">90%</span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Theme</label>
                        <select id="theme" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="default">Default</option>
                            <option value="dark">Dark</option>
                            <option value="forest">Forest</option>
                            <option value="neutral">Neutral</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Output Folder</label>
                        <div class="flex">
                            <input type="text" id="output-folder" placeholder="Select output folder..."
                                   class="flex-1 border border-gray-300 rounded-l-md px-3 py-2" readonly>
                            <button id="select-output-btn"
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-r-md">
                                Browse
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Selection -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-lg font-semibold mb-4">Select Files</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="select-files-btn" 
                            class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p class="mt-2 text-sm font-medium text-gray-900">Select Individual Files</p>
                            <p class="text-xs text-gray-500">Choose specific .mmd files</p>
                        </div>
                    </button>
                    
                    <button id="select-folder-btn" 
                            class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
                            </svg>
                            <p class="mt-2 text-sm font-medium text-gray-900">Select Folder</p>
                            <p class="text-xs text-gray-500">Process all .mmd files in folder</p>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Files List -->
            <div id="files-section" class="bg-white rounded-lg shadow-md p-6 hidden">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">Selected Files</h2>
                    <div class="flex space-x-2">
                        <button id="convert-btn" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:bg-gray-400">
                            Convert All
                        </button>
                        <button id="clear-btn" 
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                            Clear All
                        </button>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div id="progress-section" class="mb-4 hidden">
                    <div class="flex items-center justify-between mb-2">
                        <span id="progress-text" class="text-sm text-gray-600">Processing...</span>
                        <span id="progress-percentage" class="text-sm text-gray-600">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- Files List -->
                <div id="files-list" class="space-y-2 max-h-96 overflow-y-auto"></div>
            </div>

            <!-- Results Section -->
            <div id="results-section" class="bg-white rounded-lg shadow-md p-6 mt-6 hidden">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">Conversion Results</h2>
                    <button id="open-output-btn" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md">
                        Open Output Folder
                    </button>
                </div>
                <div id="results-summary" class="mb-4"></div>
                <div id="results-list" class="space-y-2"></div>
            </div>
            </div> <!-- End convert-tab -->
        </main>
    </div>

    <script>
        // Application state
        let selectedFiles = [];
        let isConverting = false;
        let outputFolder = '';
        let conversionResults = [];
        let activeTab = 'generate';
        let aiResult = null;

        // DOM elements
        const elements = {
            // Tab elements
            tabGenerate: document.getElementById('tab-generate'),
            tabConvert: document.getElementById('tab-convert'),
            generateTab: document.getElementById('generate-tab'),
            convertTab: document.getElementById('convert-tab'),

            // AI Generation elements
            aiDescription: document.getElementById('ai-description'),
            aiOutputFormat: document.getElementById('ai-output-format'),
            aiTemplate: document.getElementById('ai-template'),
            aiTheme: document.getElementById('ai-theme'),
            aiGenerateBtn: document.getElementById('ai-generate-btn'),
            aiSaveBtn: document.getElementById('ai-save-btn'),
            aiResultsSection: document.getElementById('ai-results-section'),
            aiResultsContent: document.getElementById('ai-results-content'),

            // Convert elements
            formatSelect: document.getElementById('format'),
            qualitySlider: document.getElementById('quality'),
            qualityValue: document.getElementById('quality-value'),
            themeSelect: document.getElementById('theme'),
            outputFolderInput: document.getElementById('output-folder'),
            selectOutputBtn: document.getElementById('select-output-btn'),
            selectFilesBtn: document.getElementById('select-files-btn'),
            selectFolderBtn: document.getElementById('select-folder-btn'),
            filesSection: document.getElementById('files-section'),
            filesList: document.getElementById('files-list'),
            convertBtn: document.getElementById('convert-btn'),
            clearBtn: document.getElementById('clear-btn'),
            progressSection: document.getElementById('progress-section'),
            progressBar: document.getElementById('progress-bar'),
            progressText: document.getElementById('progress-text'),
            progressPercentage: document.getElementById('progress-percentage'),
            resultsSection: document.getElementById('results-section'),
            resultsSummary: document.getElementById('results-summary'),
            resultsList: document.getElementById('results-list'),
            openOutputBtn: document.getElementById('open-output-btn'),
            appInfo: document.getElementById('app-info')
        };

        // Initialize app
        async function initializeApp() {
            // Load app info
            try {
                const appInfo = await window.electronAPI.getAppInfo();
                elements.appInfo.textContent = `${appInfo.name} v${appInfo.version} (${appInfo.platform})`;
            } catch (error) {
                console.error('Failed to load app info:', error);
            }

            // Setup event listeners
            setupEventListeners();

            // Setup conversion progress listener
            window.electronAPI.onConversionProgress((data) => {
                updateProgress(data.completed, data.total, data.currentFile);
            });
        }

        function setupEventListeners() {
            // Tab switching
            elements.tabGenerate.addEventListener('click', () => switchTab('generate'));
            elements.tabConvert.addEventListener('click', () => switchTab('convert'));

            // AI Generation
            elements.aiGenerateBtn.addEventListener('click', generateAIDiagram);
            elements.aiSaveBtn.addEventListener('click', saveAIResult);

            // Quality slider
            elements.qualitySlider.addEventListener('input', (e) => {
                elements.qualityValue.textContent = `${e.target.value}%`;
            });

            // Output folder selection
            elements.selectOutputBtn.addEventListener('click', selectOutputFolder);

            // File selection
            elements.selectFilesBtn.addEventListener('click', selectFiles);
            elements.selectFolderBtn.addEventListener('click', selectFolder);

            // Conversion controls
            elements.convertBtn.addEventListener('click', startConversion);
            elements.clearBtn.addEventListener('click', clearFiles);

            // Results
            elements.openOutputBtn.addEventListener('click', openOutputFolder);
        }

        function switchTab(tab) {
            activeTab = tab;

            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            if (tab === 'generate') {
                elements.tabGenerate.classList.remove('border-transparent', 'text-gray-500');
                elements.tabGenerate.classList.add('border-blue-500', 'text-blue-600');
                elements.generateTab.classList.remove('hidden');
            } else {
                elements.tabConvert.classList.remove('border-transparent', 'text-gray-500');
                elements.tabConvert.classList.add('border-blue-500', 'text-blue-600');
                elements.convertTab.classList.remove('hidden');
            }
        }

        async function selectOutputFolder() {
            try {
                const result = await window.electronAPI.selectOutputFolder();
                if (!result.canceled && result.path) {
                    outputFolder = result.path;
                    elements.outputFolderInput.value = result.path;
                }
            } catch (error) {
                console.error('Failed to select output folder:', error);
                alert('Failed to select output folder: ' + error.message);
            }
        }

        async function selectFiles() {
            try {
                const result = await window.electronAPI.selectFiles();
                if (!result.canceled && result.files) {
                    addFiles(result.files);
                }
            } catch (error) {
                console.error('Failed to select files:', error);
                alert('Failed to select files: ' + error.message);
            }
        }

        async function selectFolder() {
            try {
                const result = await window.electronAPI.selectFolder();
                if (!result.canceled && result.path) {
                    // For now, just show a message that folder processing would be implemented
                    alert('Folder processing will scan for .mmd files in the selected folder. This feature needs additional implementation.');
                }
            } catch (error) {
                console.error('Failed to select folder:', error);
                alert('Failed to select folder: ' + error.message);
            }
        }

        function addFiles(files) {
            // Filter for .mmd files only
            const mmdFiles = files.filter(file => file.name.toLowerCase().endsWith('.mmd'));

            if (mmdFiles.length === 0) {
                alert('No .mmd files found in selection.');
                return;
            }

            // Add to selected files (avoid duplicates)
            mmdFiles.forEach(file => {
                if (!selectedFiles.find(f => f.path === file.path)) {
                    selectedFiles.push({
                        ...file,
                        id: Date.now() + Math.random(),
                        status: 'pending'
                    });
                }
            });

            updateFilesDisplay();
        }

        function updateFilesDisplay() {
            if (selectedFiles.length === 0) {
                elements.filesSection.classList.add('hidden');
                return;
            }

            elements.filesSection.classList.remove('hidden');
            elements.filesList.innerHTML = '';

            selectedFiles.forEach(file => {
                const fileElement = createFileElement(file);
                elements.filesList.appendChild(fileElement);
            });

            // Update convert button state
            elements.convertBtn.disabled = isConverting || selectedFiles.length === 0;
        }

        function createFileElement(file) {
            const div = document.createElement('div');
            div.className = 'file-item flex items-center justify-between p-3 border rounded-lg';

            const statusClass = `status-${file.status}`;
            const statusIcon = getStatusIcon(file.status);

            div.innerHTML = `
                <div class="flex items-center space-x-3">
                    <span class="${statusClass}">${statusIcon}</span>
                    <div>
                        <p class="font-medium text-gray-900">${file.name}</p>
                        <p class="text-sm text-gray-500">${(file.size / 1024).toFixed(1)} KB</p>
                    </div>
                </div>
                <button onclick="removeFile('${file.id}')" class="text-red-500 hover:text-red-700">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;

            return div;
        }

        function getStatusIcon(status) {
            switch (status) {
                case 'pending': return '⏳';
                case 'processing': return '<span class="spinner">⚙️</span>';
                case 'completed': return '✅';
                case 'error': return '❌';
                default: return '⏳';
            }
        }

        function removeFile(fileId) {
            selectedFiles = selectedFiles.filter(f => f.id !== fileId);
            updateFilesDisplay();
        }

        function clearFiles() {
            selectedFiles = [];
            conversionResults = [];
            updateFilesDisplay();
            elements.resultsSection.classList.add('hidden');
            elements.progressSection.classList.add('hidden');
        }

        async function startConversion() {
            if (selectedFiles.length === 0) {
                alert('Please select files to convert.');
                return;
            }

            if (!outputFolder) {
                alert('Please select an output folder.');
                return;
            }

            isConverting = true;
            elements.convertBtn.disabled = true;
            elements.progressSection.classList.remove('hidden');

            // Reset file statuses
            selectedFiles.forEach(file => file.status = 'pending');
            updateFilesDisplay();

            try {
                const qualityPreset = document.getElementById('quality-preset').value;
                const options = {
                    format: elements.formatSelect.value,
                    quality: parseInt(elements.qualitySlider.value),
                    theme: elements.themeSelect.value,
                    outputDir: outputFolder,
                    width: 1920,
                    height: 1080,
                    backgroundColor: '#ffffff',
                    qualityPreset: qualityPreset || undefined
                };

                const results = await window.electronAPI.convertBatch(selectedFiles, options);
                conversionResults = results;

                // Update file statuses based on results
                results.forEach(result => {
                    const file = selectedFiles.find(f => f.name === result.originalFile);
                    if (file) {
                        file.status = result.success ? 'completed' : 'error';
                        file.error = result.error;
                    }
                });

                updateFilesDisplay();
                showResults(results);

            } catch (error) {
                console.error('Conversion failed:', error);
                alert('Conversion failed: ' + error.message);
            } finally {
                isConverting = false;
                elements.convertBtn.disabled = false;
                elements.progressSection.classList.add('hidden');
            }
        }

        function updateProgress(completed, total, currentFile) {
            const percentage = Math.round((completed / total) * 100);
            elements.progressBar.style.width = `${percentage}%`;
            elements.progressPercentage.textContent = `${percentage}%`;
            elements.progressText.textContent = `Processing: ${currentFile} (${completed}/${total})`;
        }

        function showResults(results) {
            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;

            elements.resultsSummary.innerHTML = `
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="bg-blue-50 p-3 rounded">
                        <p class="text-2xl font-bold text-blue-600">${results.length}</p>
                        <p class="text-sm text-blue-600">Total Files</p>
                    </div>
                    <div class="bg-green-50 p-3 rounded">
                        <p class="text-2xl font-bold text-green-600">${successful}</p>
                        <p class="text-sm text-green-600">Successful</p>
                    </div>
                    <div class="bg-red-50 p-3 rounded">
                        <p class="text-2xl font-bold text-red-600">${failed}</p>
                        <p class="text-sm text-red-600">Failed</p>
                    </div>
                </div>
            `;

            elements.resultsList.innerHTML = '';
            results.forEach(result => {
                const resultElement = document.createElement('div');
                resultElement.className = `p-3 border rounded-lg ${result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;

                resultElement.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium ${result.success ? 'text-green-900' : 'text-red-900'}">${result.originalFile}</p>
                            ${result.success
                                ? `<p class="text-sm text-green-600">Converted successfully</p>`
                                : `<p class="text-sm text-red-600">Error: ${result.error}</p>`
                            }
                        </div>
                        <span class="text-2xl">${result.success ? '✅' : '❌'}</span>
                    </div>
                `;

                elements.resultsList.appendChild(resultElement);
            });

            elements.resultsSection.classList.remove('hidden');
        }

        async function openOutputFolder() {
            if (outputFolder) {
                try {
                    await window.electronAPI.openOutputFolder(outputFolder);
                } catch (error) {
                    console.error('Failed to open output folder:', error);
                    alert('Failed to open output folder: ' + error.message);
                }
            }
        }

        async function generateAIDiagram() {
            const description = elements.aiDescription.value.trim();
            if (!description) {
                alert('Please enter a description for your diagram.');
                return;
            }

            elements.aiGenerateBtn.disabled = true;
            elements.aiGenerateBtn.textContent = 'Generating...';

            try {
                const result = await window.electronAPI.generateAIDiagram({
                    description,
                    outputFormat: elements.aiOutputFormat.value,
                    template: elements.aiTemplate.value || undefined,
                    theme: elements.aiTheme.value,
                });

                if (result.success) {
                    aiResult = result;
                    displayAIResult(result);
                    elements.aiSaveBtn.classList.remove('hidden');
                } else {
                    throw new Error(result.error || 'Generation failed');
                }
            } catch (error) {
                console.error('AI generation failed:', error);
                alert('AI generation failed: ' + error.message);
            } finally {
                elements.aiGenerateBtn.disabled = false;
                elements.aiGenerateBtn.textContent = 'Generate Diagram';
            }
        }

        function displayAIResult(result) {
            elements.aiResultsSection.classList.remove('hidden');

            if (result.outputFormat === 'mermaid') {
                // Display Mermaid code
                elements.aiResultsContent.innerHTML = `
                    <div class="space-y-4">
                        <div class="bg-gray-100 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Generated Mermaid Code</h3>
                            <pre class="text-sm font-mono whitespace-pre-wrap overflow-x-auto">${result.content}</pre>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="copyToClipboard('${result.content.replace(/'/g, "\\'")}', this)"
                                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-semibold">
                                Copy Code
                            </button>
                        </div>
                    </div>
                `;
            } else {
                // Display image
                const imageUrl = `data:image/${result.outputFormat === 'jpg' ? 'jpeg' : result.outputFormat};base64,${result.imageData}`;
                elements.aiResultsContent.innerHTML = `
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg text-center">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Generated ${result.outputFormat.toUpperCase()} Image</h3>
                            <img src="${imageUrl}" alt="Generated diagram" class="max-w-full h-auto mx-auto" style="max-height: 500px;">
                        </div>
                    </div>
                `;
            }
        }

        async function saveAIResult() {
            if (!aiResult) return;

            try {
                const saved = await window.electronAPI.saveAIResult(aiResult);
                if (saved.success) {
                    alert(`File saved successfully to: ${saved.filePath}`);
                } else {
                    throw new Error(saved.error || 'Save failed');
                }
            } catch (error) {
                console.error('Save failed:', error);
                alert('Save failed: ' + error.message);
            }
        }

        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
                button.classList.add('bg-green-600', 'hover:bg-green-700');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-600', 'hover:bg-green-700');
                    button.classList.add('bg-gray-600', 'hover:bg-gray-700');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                alert('Failed to copy to clipboard');
            });
        }

        // Make functions available globally
        window.removeFile = removeFile;
        window.copyToClipboard = copyToClipboard;

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
